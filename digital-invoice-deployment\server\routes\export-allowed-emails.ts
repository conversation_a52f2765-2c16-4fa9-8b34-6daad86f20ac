import { Router, Request, Response } from 'express';
import { storage } from '../storage-factory';
import { exportAllowedEmailsToExcel } from '../utils/excel-export';

export const exportAllowedEmailsRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  if (req.session && req.session.isAdmin) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Export allowed emails to Excel
exportAllowedEmailsRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    console.log('Exporting allowed emails to Excel...');
    const emails = await storage.getAllowedEmails();
    
    // Generate Excel file
    const buffer = await exportAllowedEmailsToExcel(emails);
    
    // Set headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=allowed-emails.xlsx');
    res.setHeader('Content-Length', buffer.length);
    
    // Send the file
    res.send(buffer);
  } catch (error) {
    console.error('Error exporting emails to Excel:', error);
    res.status(500).json({ message: 'Failed to export emails to Excel' });
  }
});
