import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import AdminLayout from '@/components/admin/AdminLayout';

interface BusinessFacade {
  id: number;
  name: string;
  type: string;
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  description?: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface FacadeType {
  id: string;
  name: string;
}

const BusinessFacades: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingFacade, setEditingFacade] = useState<BusinessFacade | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    logoUrl: '',
    primaryColor: '',
    secondaryColor: '',
    description: '',
    active: true
  });

  // Fetch business facades
  const { data: facades, isLoading: isLoadingFacades } = useQuery({
    queryKey: ['businessFacades'],
    queryFn: async () => {
      const response = await axios.get('/api/business-facades');
      return response.data;
    }
  });

  // Fetch facade types
  const { data: facadeTypes, isLoading: isLoadingTypes } = useQuery({
    queryKey: ['facadeTypes'],
    queryFn: async () => {
      const response = await axios.get('/api/business-facades/types/all');
      return response.data;
    }
  });

  // Create business facade mutation
  const createFacadeMutation = useMutation({
    mutationFn: async (facade: any) => {
      const response = await axios.post('/api/business-facades', facade);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['businessFacades'] });
      setDialogOpen(false);
      resetForm();
      toast({
        title: 'Success',
        description: 'Business facade created successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create business facade',
        variant: 'destructive',
      });
    }
  });

  // Update business facade mutation
  const updateFacadeMutation = useMutation({
    mutationFn: async ({ id, facade }: { id: number, facade: any }) => {
      const response = await axios.put(`/api/business-facades/${id}`, facade);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['businessFacades'] });
      setDialogOpen(false);
      resetForm();
      toast({
        title: 'Success',
        description: 'Business facade updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update business facade',
        variant: 'destructive',
      });
    }
  });

  // Delete business facade mutation
  const deleteFacadeMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await axios.delete(`/api/business-facades/${id}`);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['businessFacades'] });
      toast({
        title: 'Success',
        description: `Business facade deleted successfully${data.updatedDomains?.length ? '. Associated domains have been updated.' : ''}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to delete business facade',
        variant: 'destructive',
      });
    }
  });

  const resetForm = () => {
    setFormData({
      name: '',
      type: '',
      logoUrl: '',
      primaryColor: '',
      secondaryColor: '',
      description: '',
      active: true
    });
    setEditingFacade(null);
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingFacade) {
      updateFacadeMutation.mutate({ id: editingFacade.id, facade: formData });
    } else {
      createFacadeMutation.mutate(formData);
    }
  };

  const handleEdit = (facade: BusinessFacade) => {
    setEditingFacade(facade);
    setFormData({
      name: facade.name,
      type: facade.type,
      logoUrl: facade.logoUrl || '',
      primaryColor: facade.primaryColor || '',
      secondaryColor: facade.secondaryColor || '',
      description: facade.description || '',
      active: facade.active
    });
    setDialogOpen(true);
  };

  const handleDelete = (id: number) => {
    deleteFacadeMutation.mutate(id);
  };

  return (
    <AdminLayout>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Business Facades</h1>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>Add Business Facade</Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{editingFacade ? 'Edit Business Facade' : 'Add Business Facade'}</DialogTitle>
              <DialogDescription>
                {editingFacade ? 'Update business facade details' : 'Add a new business facade template'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleFormChange}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="type" className="text-right">Type</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => handleSelectChange('type', value)}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select a type" />
                    </SelectTrigger>
                    <SelectContent>
                      {facadeTypes?.map((type: FacadeType) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="logoUrl" className="text-right">Logo URL</Label>
                  <Input
                    id="logoUrl"
                    name="logoUrl"
                    value={formData.logoUrl}
                    onChange={handleFormChange}
                    className="col-span-3"
                    placeholder="https://example.com/logo.png"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="primaryColor" className="text-right">Primary Color</Label>
                  <div className="col-span-3 flex gap-2">
                    <Input
                      id="primaryColor"
                      name="primaryColor"
                      value={formData.primaryColor}
                      onChange={handleFormChange}
                      className="flex-1"
                      placeholder="#4CAF50"
                    />
                    {formData.primaryColor && (
                      <div 
                        className="w-10 h-10 rounded border" 
                        style={{ backgroundColor: formData.primaryColor }}
                      />
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="secondaryColor" className="text-right">Secondary Color</Label>
                  <div className="col-span-3 flex gap-2">
                    <Input
                      id="secondaryColor"
                      name="secondaryColor"
                      value={formData.secondaryColor}
                      onChange={handleFormChange}
                      className="flex-1"
                      placeholder="#8BC34A"
                    />
                    {formData.secondaryColor && (
                      <div 
                        className="w-10 h-10 rounded border" 
                        style={{ backgroundColor: formData.secondaryColor }}
                      />
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleFormChange}
                    className="col-span-3"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="active" className="text-right">Active</Label>
                  <div className="col-span-3 flex items-center space-x-2">
                    <Switch
                      id="active"
                      checked={formData.active}
                      onCheckedChange={(checked) => handleSwitchChange('active', checked)}
                    />
                    <Label htmlFor="active">Facade is active</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={createFacadeMutation.isPending || updateFacadeMutation.isPending}>
                  {editingFacade ? 'Update' : 'Add'} Business Facade
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Business Facades</CardTitle>
          <CardDescription>Manage business facade templates for your domains</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingFacades ? (
            <div>Loading business facades...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Colors</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {facades?.map((facade: BusinessFacade) => (
                  <TableRow key={facade.id}>
                    <TableCell>{facade.name}</TableCell>
                    <TableCell>
                      {facadeTypes?.find((t: FacadeType) => t.id === facade.type)?.name || facade.type}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {facade.primaryColor && (
                          <div 
                            className="w-6 h-6 rounded border" 
                            style={{ backgroundColor: facade.primaryColor }}
                            title="Primary Color"
                          />
                        )}
                        {facade.secondaryColor && (
                          <div 
                            className="w-6 h-6 rounded border" 
                            style={{ backgroundColor: facade.secondaryColor }}
                            title="Secondary Color"
                          />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={facade.active ? 'default' : 'outline'}>
                        {facade.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => handleEdit(facade)}>
                          Edit
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="destructive" size="sm">Delete</Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This will permanently delete the business facade. Any domains using this facade will have their facade reference removed.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDelete(facade.id)}>
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </AdminLayout>
  );
};

export default BusinessFacades;
