#!/bin/bash

# CloudPanel.io Startup Script for Digital Invoice Application (Fixed)

echo "🚀 Starting Digital Invoice Application..."

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found, copying from .env.production"
    cp .env.production .env
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install --production
fi

# Install cross-env globally if not available
if ! command -v cross-env &> /dev/null; then
    echo "📦 Installing cross-env..."
    npm install -g cross-env
fi

# Check if PM2 is available (recommended for production)
if command -v pm2 &> /dev/null; then
    echo "▶️  Starting application with PM2..."
    pm2 start dist/index.js --name "digital-invoice" --env production
    echo "✅ Application started with PM2"
    echo "📊 Check status: pm2 status"
    echo "📋 View logs: pm2 logs digital-invoice"
else
    echo "▶️  Starting application with npm..."
    npm start
fi
