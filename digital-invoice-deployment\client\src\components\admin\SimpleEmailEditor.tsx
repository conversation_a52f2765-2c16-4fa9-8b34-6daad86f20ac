import React, { useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface SimpleEmailEditorProps {
  value: string;
  onChange: (value: string) => void;
}

const SimpleEmailEditor: React.FC<SimpleEmailEditorProps> = ({ value, onChange }) => {
  const editorRef = useRef<HTMLDivElement>(null);

  // Initialize editor with content
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  // Handle content change
  const handleContentChange = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  // Apply formatting
  const applyFormatting = (command: string, value: string = '') => {
    document.execCommand('styleWithCSS', false, 'true');
    document.execCommand(command, false, value);
    handleContentChange();
    editorRef.current?.focus();
  };

  // Insert username
  const insertUsername = () => {
    const username = prompt('Enter username:');
    if (username) {
      document.execCommand('insertHTML', false, `Username: ${username}`);
      handleContentChange();
    }
  };

  // Insert link
  const insertLink = () => {
    const url = prompt('Enter link URL:');
    if (url) {
      document.execCommand('createLink', false, url);
      handleContentChange();
    }
  };

  return (
    <div className="border rounded-md overflow-hidden">
      {/* Toolbar */}
      <div className="bg-muted p-2 flex flex-wrap gap-1 border-b">
        <Button 
          type="button" 
          variant="ghost" 
          size="sm"
          onClick={() => applyFormatting('bold')}
          title="Bold"
        >
          Bold
        </Button>
        <Button 
          type="button" 
          variant="ghost" 
          size="sm"
          onClick={() => applyFormatting('italic')}
          title="Italic"
        >
          Italic
        </Button>
        <Button 
          type="button" 
          variant="ghost" 
          size="sm"
          onClick={() => applyFormatting('underline')}
          title="Underline"
        >
          Underline
        </Button>
        <Button 
          type="button" 
          variant="ghost" 
          size="sm"
          onClick={() => applyFormatting('insertUnorderedList')}
          title="Bullet List"
        >
          Bullet List
        </Button>
        <Button 
          type="button" 
          variant="ghost" 
          size="sm"
          onClick={() => applyFormatting('insertOrderedList')}
          title="Numbered List"
        >
          Numbered List
        </Button>
        <Button 
          type="button" 
          variant="ghost" 
          size="sm"
          onClick={insertLink}
          title="Insert Link"
        >
          Link
        </Button>
        <Button 
          type="button" 
          variant="ghost" 
          size="sm"
          onClick={insertUsername}
          title="Insert Username"
        >
          Username
        </Button>
      </div>
      
      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        onInput={handleContentChange}
        onBlur={handleContentChange}
        className="p-4 min-h-[300px] focus:outline-none"
        style={{ 
          minHeight: '300px',
          direction: 'ltr',
          textAlign: 'left',
          unicodeBidi: 'normal'
        }}
      />
    </div>
  );
};

export default SimpleEmailEditor;
