#!/usr/bin/env node

/**
 * Deployment Package Creator for CloudPanel.io
 *
 * This script creates a deployment-ready package for your Digital Invoice application
 *
 * Usage: node create-deployment-package.js
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Creating CloudPanel.io Deployment Package...\n');

// Configuration
const PACKAGE_NAME = 'digital-invoice-deployment';
const EXCLUDE_PATTERNS = [
  'node_modules',
  '.git',
  '.env',
  '.env.local',
  'README-CLOUDPANEL-DEPLOYMENT.md',
  'DEPLOYMENT-CHECKLIST.md',
  'migrate-to-mysql.js',
  'create-deployment-package.js',
  '*.log',
  'logs',
  'backups',
  '.vscode',
  '.idea',
  'thumbs.db',
  '.DS_Store'
];

function createDeploymentPackage() {
  try {
    // Step 1: Verify build exists
    console.log('📦 Step 1: Verifying production build...');
    if (!fs.existsSync('./dist/index.js')) {
      console.error('❌ Production build not found. Please run: npm run build:prod');
      process.exit(1);
    }
    if (!fs.existsSync('./dist/public/index.html')) {
      console.error('❌ Frontend build not found. Please run: npm run build:prod');
      process.exit(1);
    }
    console.log('✅ Production build verified');

    // Step 2: Create package directory
    console.log('\n📁 Step 2: Creating package directory...');
    const packageDir = `./${PACKAGE_NAME}`;
    if (fs.existsSync(packageDir)) {
      fs.rmSync(packageDir, { recursive: true, force: true });
    }
    fs.mkdirSync(packageDir, { recursive: true });
    console.log(`✅ Created directory: ${packageDir}`);

    // Step 3: Copy essential files
    console.log('\n📋 Step 3: Copying essential files...');
    
    const filesToCopy = [
      'package.json',
      'package-lock.json',
      '.env.production',
      'drizzle.config.ts',
      'tsconfig.json',
      'postcss.config.js',
      'tailwind.config.ts',
      'vite.config.ts',
      'components.json'
    ];

    filesToCopy.forEach(file => {
      if (fs.existsSync(file)) {
        fs.copyFileSync(file, path.join(packageDir, file));
        console.log(`   ✅ Copied: ${file}`);
      } else {
        console.log(`   ⚠️  Skipped (not found): ${file}`);
      }
    });

    // Step 4: Copy directories
    console.log('\n📂 Step 4: Copying directories...');
    
    const dirsToCopy = [
      'dist',
      'shared',
      'server',
      'client',
      'src',
      'uploads'
    ];

    dirsToCopy.forEach(dir => {
      if (fs.existsSync(dir)) {
        copyDirectory(dir, path.join(packageDir, dir));
        console.log(`   ✅ Copied directory: ${dir}`);
      } else {
        console.log(`   ⚠️  Skipped (not found): ${dir}`);
      }
    });

    // Step 5: Copy database (if exists)
    console.log('\n🗄️ Step 5: Copying database...');
    if (fs.existsSync('data.db')) {
      fs.copyFileSync('data.db', path.join(packageDir, 'data.db'));
      console.log('   ✅ Copied: data.db');
    } else {
      console.log('   ⚠️  No database file found (data.db)');
    }

    // Step 6: Create deployment instructions
    console.log('\n📝 Step 6: Creating deployment instructions...');
    createDeploymentInstructions(packageDir);
    console.log('   ✅ Created: DEPLOYMENT-INSTRUCTIONS.txt');

    // Step 7: Create startup script
    console.log('\n🔧 Step 7: Creating startup script...');
    createStartupScript(packageDir);
    console.log('   ✅ Created: start.sh');

    // Step 8: Create package info
    console.log('\n📊 Step 8: Creating package info...');
    createPackageInfo(packageDir);
    console.log('   ✅ Created: PACKAGE-INFO.txt');

    // Step 9: Create archive
    console.log('\n📦 Step 9: Creating deployment archive...');
    const archiveName = `${PACKAGE_NAME}-${new Date().toISOString().slice(0, 10)}.tar.gz`;
    
    try {
      execSync(`tar -czf ${archiveName} -C ${packageDir} .`, { stdio: 'inherit' });
      console.log(`   ✅ Created archive: ${archiveName}`);
    } catch (error) {
      console.log('   ⚠️  tar command failed, trying alternative method...');
      // Alternative method for Windows or systems without tar
      console.log('   💡 Please manually compress the folder:', packageDir);
    }

    // Step 10: Summary
    console.log('\n🎉 Deployment package created successfully!');
    console.log('\n📋 Package Contents:');
    console.log(`   📁 Directory: ${packageDir}`);
    if (fs.existsSync(archiveName)) {
      console.log(`   📦 Archive: ${archiveName}`);
    }
    
    console.log('\n🚀 Next Steps:');
    console.log('   1. Upload the package to your CloudPanel.io server');
    console.log('   2. Extract the files in your site directory');
    console.log('   3. Follow the DEPLOYMENT-INSTRUCTIONS.txt file');
    console.log('   4. Configure your environment variables');
    console.log('   5. Start the application');

    console.log('\n📖 For detailed instructions, see:');
    console.log('   - README-CLOUDPANEL-DEPLOYMENT.md');
    console.log('   - DEPLOYMENT-CHECKLIST.md');

  } catch (error) {
    console.error('\n❌ Error creating deployment package:', error.message);
    process.exit(1);
  }
}

function copyDirectory(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    // Skip excluded patterns
    if (EXCLUDE_PATTERNS.some(pattern => {
      if (pattern.includes('*')) {
        return entry.name.includes(pattern.replace('*', ''));
      }
      return entry.name === pattern;
    })) {
      continue;
    }

    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

function createDeploymentInstructions(packageDir) {
  const instructions = `# CloudPanel.io Deployment Instructions

## Quick Start

1. **Upload Files**
   - Upload this entire package to your CloudPanel.io server
   - Extract to your site's htdocs directory

2. **Install Dependencies**
   \`\`\`bash
   npm install --production
   \`\`\`

3. **Configure Environment**
   - Copy .env.production to .env
   - Update database credentials if using MySQL
   - Update SESSION_SECRET and ADMIN_ACCESS_TOKEN

4. **Set CloudPanel Configuration**
   - App Type: Node.js
   - App Port: 3001
   - Startup File: dist/index.js
   - Node.js Version: 18+

5. **Start Application**
   \`\`\`bash
   npm start
   \`\`\`

## Environment Variables

Required variables in .env:
- DATABASE_URL=sqlite:./data.db (or MySQL URL)
- SESSION_SECRET=your-secure-session-secret
- NODE_ENV=production
- SECURE_COOKIES=true
- ADMIN_ACCESS_TOKEN=your-admin-token

## Database Options

### SQLite (Default)
- No additional setup required
- Database file: data.db

### MySQL (Optional)
- Create MySQL database in CloudPanel
- Update DATABASE_URL in .env
- Run: npm run db:push

## Troubleshooting

- Check CloudPanel logs for errors
- Verify Node.js version (18+)
- Ensure all environment variables are set
- Check file permissions

For detailed instructions, see README-CLOUDPANEL-DEPLOYMENT.md
`;

  fs.writeFileSync(path.join(packageDir, 'DEPLOYMENT-INSTRUCTIONS.txt'), instructions);
}

function createStartupScript(packageDir) {
  const script = `#!/bin/bash

# CloudPanel.io Startup Script for Digital Invoice Application

echo "🚀 Starting Digital Invoice Application..."

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found, copying from .env.production"
    cp .env.production .env
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install --production
fi

# Start the application
echo "▶️  Starting application..."
npm start
`;

  fs.writeFileSync(path.join(packageDir, 'start.sh'), script);
  
  // Make script executable (Unix systems)
  try {
    fs.chmodSync(path.join(packageDir, 'start.sh'), '755');
  } catch (error) {
    // Ignore chmod errors on Windows
  }
}

function createPackageInfo(packageDir) {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const buildDate = new Date().toISOString();
  
  const info = `# Digital Invoice Application - Deployment Package

## Package Information
- Application: ${packageJson.name}
- Version: ${packageJson.version}
- Build Date: ${buildDate}
- Node.js Version Required: 18+

## Package Contents
- ✅ Production build (dist/)
- ✅ Server code (server/)
- ✅ Shared modules (shared/)
- ✅ Client source (client/)
- ✅ Configuration files
- ✅ Database file (if exists)
- ✅ Upload directory (if exists)

## Deployment Target
- Platform: CloudPanel.io
- Database: SQLite (with MySQL support)
- Port: 3001

## Features Included
- User authentication with 2FA
- Invoice management system
- Custom checkout pages
- Email templates and SMTP
- PayPal integration
- Telegram bot integration
- File upload functionality
- System monitoring
- Admin dashboard

## Support
For deployment issues, check:
1. DEPLOYMENT-INSTRUCTIONS.txt
2. README-CLOUDPANEL-DEPLOYMENT.md
3. DEPLOYMENT-CHECKLIST.md

Generated on: ${buildDate}
`;

  fs.writeFileSync(path.join(packageDir, 'PACKAGE-INFO.txt'), info);
}

// Run the script
createDeploymentPackage();
