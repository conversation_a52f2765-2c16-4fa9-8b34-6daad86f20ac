import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardT<PERSON>le, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Copy, Download, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

export default function RecoveryCodes() {
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);
  const [recoveryCodes, setRecoveryCodes] = useState<string[]>([]);
  const [showCodes, setShowCodes] = useState(false);

  // Generate recovery codes
  const generateRecoveryCodes = async () => {
    setIsGenerating(true);
    try {
      const response = await apiRequest('/api/admin/recovery-codes/generate', 'POST');
      setRecoveryCodes(response.codes);
      setShowCodes(true);
      toast({
        title: 'Recovery codes generated',
        description: 'Please save these codes in a safe place. They will only be shown once.',
      });
    } catch (error) {
      console.error('Error generating recovery codes:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to generate recovery codes. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Copy codes to clipboard
  const copyToClipboard = () => {
    const codesText = recoveryCodes.join('\n');
    navigator.clipboard.writeText(codesText);
    toast({
      title: 'Copied to clipboard',
      description: 'Recovery codes have been copied to your clipboard.',
    });
  };

  // Download codes as a text file
  const downloadCodes = () => {
    const codesText = recoveryCodes.join('\n');
    const blob = new Blob([codesText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'recovery-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast({
      title: 'Downloaded',
      description: 'Recovery codes have been downloaded.',
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Recovery Codes</CardTitle>
        <CardDescription>
          Generate recovery codes to use if you lose access to your authenticator app
        </CardDescription>
      </CardHeader>
      <CardContent>
        {showCodes ? (
          <>
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                Save these recovery codes in a safe place. Each code can only be used once. If you lose access to your authenticator app, you can use one of these codes to sign in.
              </AlertDescription>
            </Alert>
            <div className="grid grid-cols-2 gap-2 mb-4">
              {recoveryCodes.map((code, index) => (
                <div key={index} className="p-2 bg-muted rounded-md font-mono text-center">
                  {code}
                </div>
              ))}
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={copyToClipboard}>
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
              <Button variant="outline" onClick={downloadCodes}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="mb-4 text-muted-foreground">
              Recovery codes allow you to sign in if you lose access to your authenticator app. Each code can only be used once.
            </p>
            <Button
              onClick={generateRecoveryCodes}
              disabled={isGenerating}
            >
              {isGenerating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Generate Recovery Codes
            </Button>
          </div>
        )}
      </CardContent>
      {showCodes && (
        <CardFooter>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => {
              setShowCodes(false);
              setRecoveryCodes([]);
            }}
          >
            I've saved these codes
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
