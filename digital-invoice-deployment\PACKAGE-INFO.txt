# Digital Invoice Application - Deployment Package

## Package Information
- Application: rest-express
- Version: 1.0.0
- Build Date: 2025-06-05T02:37:19.532Z
- Node.js Version Required: 18+

## Package Contents
- ✅ Production build (dist/)
- ✅ Server code (server/)
- ✅ Shared modules (shared/)
- ✅ Client source (client/)
- ✅ Configuration files
- ✅ Database file (if exists)
- ✅ Upload directory (if exists)

## Deployment Target
- Platform: CloudPanel.io
- Database: SQLite (with MySQL support)
- Port: 3001

## Features Included
- User authentication with 2FA
- Invoice management system
- Custom checkout pages
- Email templates and SMTP
- PayPal integration
- Telegram bot integration
- File upload functionality
- System monitoring
- Admin dashboard

## Support
For deployment issues, check:
1. DEPLOYMENT-INSTRUCTIONS.txt
2. README-CLOUDPANEL-DEPLOYMENT.md
3. DEPLOYMENT-CHECKLIST.md

Generated on: 2025-06-05T02:37:19.532Z
