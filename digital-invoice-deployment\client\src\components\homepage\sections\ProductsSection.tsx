import React from 'react';
import { Product } from '@shared/schema';
import { type PageSection, type ProductsSection as ProductsSectionType, type ThemeSettings } from '@/api/homepage';
import ProductCard from '@/components/ProductCard';
import { Skeleton } from '@/components/ui/skeleton';

interface ProductsSectionProps {
  section: PageSection;
  theme?: ThemeSettings;
  products: Product[];
  isLoading: boolean;
  onSelectProduct: (product: Product) => void;
}

const ProductsSection: React.FC<ProductsSectionProps> = ({ 
  section, 
  theme, 
  products, 
  isLoading, 
  onSelectProduct 
}) => {
  const content = section.content as ProductsSectionType;

  // Filter products based on settings
  const getDisplayProducts = () => {
    if (content.showAllProducts) {
      return products.filter(p => p.active);
    }
    
    if (content.featuredProductIds.length > 0) {
      return products.filter(p => 
        p.active && content.featuredProductIds.includes(p.id)
      );
    }
    
    return products.filter(p => p.active);
  };

  const displayProducts = getDisplayProducts();

  // Get grid columns class based on columns setting
  const getGridClass = () => {
    const cols = Math.min(content.columns || 3, 6);
    const colsMap = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
    };
    return colsMap[cols as keyof typeof colsMap] || colsMap[3];
  };

  // Loading skeleton for products
  const ProductSkeleton = () => (
    <div className="bg-card rounded-lg overflow-hidden shadow-sm border">
      <Skeleton className="w-full h-48" />
      <div className="p-4">
        <Skeleton className="h-6 w-3/4 mb-2" />
        {content.showDescriptions && (
          <>
            <Skeleton className="h-4 w-full mb-1" />
            <Skeleton className="h-4 w-5/6 mb-3" />
          </>
        )}
        <div className="flex justify-between items-center">
          {content.showPrices && <Skeleton className="h-6 w-16" />}
          <Skeleton className="h-10 w-20 rounded-md" />
        </div>
      </div>
    </div>
  );

  // Custom ProductCard wrapper to respect section settings
  const CustomProductCard: React.FC<{ product: Product }> = ({ product }) => (
    <div className="transform transition-all duration-300 hover:scale-105">
      <div className="bg-white rounded-lg overflow-hidden shadow-sm border hover:shadow-lg transition-shadow duration-300">
        {/* Product Image */}
        {product.imageUrl && (
          <div className="aspect-video bg-gray-100 overflow-hidden">
            <img 
              src={product.imageUrl} 
              alt={product.name}
              className="w-full h-full object-cover"
              loading="lazy"
            />
          </div>
        )}
        
        <div className="p-6">
          {/* Product Name */}
          <h3 
            className="text-xl font-semibold mb-2"
            style={{ color: theme?.textColor || '#1e293b' }}
          >
            {product.name}
          </h3>
          
          {/* Product Description */}
          {content.showDescriptions && product.description && (
            <p 
              className="text-gray-600 mb-4 line-clamp-3"
              style={{ color: theme?.textColor ? `${theme.textColor}80` : '#64748b' }}
            >
              {product.description}
            </p>
          )}
          
          <div className="flex justify-between items-center">
            {/* Price */}
            {content.showPrices && (
              <span 
                className="text-2xl font-bold"
                style={{ color: theme?.primaryColor || '#0070ba' }}
              >
                ${product.price}
              </span>
            )}
            
            {/* Select Button */}
            <button
              onClick={() => onSelectProduct(product)}
              className="px-6 py-2 rounded-lg font-medium transition-all duration-300 hover:shadow-lg transform hover:scale-105"
              style={{
                backgroundColor: theme?.primaryColor || '#0070ba',
                color: '#ffffff'
              }}
            >
              Select
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Render products based on layout
  const renderProducts = () => {
    if (isLoading) {
      return (
        <div className={`grid ${getGridClass()} gap-6`}>
          {Array(content.columns || 3).fill(0).map((_, index) => (
            <ProductSkeleton key={index} />
          ))}
        </div>
      );
    }

    if (displayProducts.length === 0) {
      return (
        <div className="text-center py-12">
          <div 
            className="text-6xl mb-4 opacity-50"
            style={{ color: theme?.textColor || '#1e293b' }}
          >
            🛍️
          </div>
          <h3 
            className="text-xl font-semibold mb-2"
            style={{ color: theme?.textColor || '#1e293b' }}
          >
            No Products Available
          </h3>
          <p 
            className="text-gray-600"
            style={{ color: theme?.textColor ? `${theme.textColor}80` : '#64748b' }}
          >
            Check back later for new products.
          </p>
        </div>
      );
    }

    switch (content.layout) {
      case 'carousel':
        // TODO: Implement carousel layout
        return (
          <div className="text-center mb-8">
            <p className="text-gray-500 mb-4">Carousel layout coming soon...</p>
            <div className={`grid ${getGridClass()} gap-6`}>
              {displayProducts.map((product) => (
                <CustomProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        );

      case 'grid':
      default:
        return (
          <div className={`grid ${getGridClass()} gap-6`}>
            {displayProducts.map((product) => (
              <CustomProductCard key={product.id} product={product} />
            ))}
          </div>
        );
    }
  };

  return (
    <section 
      className="py-16 px-4"
      style={{ backgroundColor: theme?.backgroundColor || '#ffffff' }}
      id="products"
    >
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          {/* Title */}
          <h2 
            className="text-3xl md:text-4xl font-bold mb-4"
            style={{ color: theme?.textColor || '#1e293b' }}
          >
            {content.title}
          </h2>

          {/* Subtitle */}
          {content.subtitle && (
            <p 
              className="text-lg md:text-xl max-w-3xl mx-auto"
              style={{ color: theme?.textColor ? `${theme.textColor}80` : '#64748b' }}
            >
              {content.subtitle}
            </p>
          )}

          {/* Decorative line */}
          <div className="mt-6 flex justify-center">
            <div 
              className="w-20 h-1 rounded-full"
              style={{ backgroundColor: theme?.primaryColor || '#0070ba' }}
            ></div>
          </div>
        </div>

        {/* Products */}
        <div className="animate-fade-in">
          {renderProducts()}
        </div>

        {/* Show all products link */}
        {!content.showAllProducts && content.featuredProductIds.length > 0 && products.length > displayProducts.length && (
          <div className="text-center mt-12">
            <button
              onClick={() => {
                // Scroll to products or show all products
                const element = document.querySelector('#products');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg transition-all duration-300 hover:shadow-lg"
              style={{
                backgroundColor: 'transparent',
                color: theme?.primaryColor || '#0070ba',
                borderColor: theme?.primaryColor || '#0070ba'
              }}
            >
              View All Products
              <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProductsSection;
