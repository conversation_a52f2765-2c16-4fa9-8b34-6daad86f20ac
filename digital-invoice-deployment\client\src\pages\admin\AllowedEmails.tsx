import { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import AdminLayout from '@/components/admin/AdminLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Plus, Trash, Upload, Download, Send, RefreshCw, Search } from 'lucide-react';
import { format } from 'date-fns';
import QuickEmailActions from '@/components/admin/QuickEmailActions';

// Form schema for adding a new email
const emailSchema = z.object({
  email: z.string().email('Valid email is required'),
  notes: z.string().optional(),
  lastSubject: z.string().optional(),
});

// Form schema for bulk import
const bulkImportSchema = z.object({
  emails: z.string().min(1, 'Emails are required'),
});

type EmailFormValues = z.infer<typeof emailSchema>;
type BulkImportFormValues = z.infer<typeof bulkImportSchema>;

// Helper function to format dates
const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), 'MMM d, yyyy');
  } catch (error) {
    return dateString;
  }
};

export default function AllowedEmailsPage() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isBulkImportDialogOpen, setIsBulkImportDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedEmail, setSelectedEmail] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Query to fetch allowed emails
  const { data: emails, isLoading } = useQuery({
    queryKey: ['/api/allowed-emails'],
    queryFn: () => apiRequest('/api/allowed-emails', 'GET')
  });

  // Filter emails based on search query
  const filteredEmails = useMemo(() => {
    if (!emails || !searchQuery.trim()) {
      return emails || [];
    }

    const query = searchQuery.toLowerCase().trim();
    return emails.filter((email: any) =>
      email.email.toLowerCase().includes(query) ||
      (email.notes && email.notes.toLowerCase().includes(query)) ||
      (email.lastSubject && email.lastSubject.toLowerCase().includes(query))
    );
  }, [emails, searchQuery]);

  // Form for adding a new email
  const emailForm = useForm<EmailFormValues>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: '',
      notes: '',
      lastSubject: '',
    }
  });

  // Form for bulk import
  const bulkImportForm = useForm<BulkImportFormValues>({
    resolver: zodResolver(bulkImportSchema),
    defaultValues: {
      emails: '',
    }
  });

  // Mutation to add a new email
  const addEmailMutation = useMutation({
    mutationFn: (data: EmailFormValues) => {
      return apiRequest('/api/allowed-emails', 'POST', data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/allowed-emails'] });
      setIsAddDialogOpen(false);
      emailForm.reset();
      toast({
        title: 'Success',
        description: 'Email added successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add email',
        variant: 'destructive',
      });
    }
  });

  // Mutation to delete an email
  const deleteEmailMutation = useMutation({
    mutationFn: (id: number) => {
      return apiRequest(`/api/allowed-emails/${id}`, 'DELETE');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/allowed-emails'] });
      setIsDeleteDialogOpen(false);
      setSelectedEmail(null);
      toast({
        title: 'Success',
        description: 'Email deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete email',
        variant: 'destructive',
      });
    }
  });

  // Mutation to bulk import emails
  const bulkImportMutation = useMutation({
    mutationFn: (data: BulkImportFormValues) => {
      return apiRequest('/api/allowed-emails/bulk', 'POST', data);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/allowed-emails'] });
      setIsBulkImportDialogOpen(false);
      bulkImportForm.reset();
      toast({
        title: 'Success',
        description: data.message || `Successfully imported emails`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to import emails',
        variant: 'destructive',
      });
    }
  });

  // Form submission handlers
  const onAddSubmit = (data: EmailFormValues) => {
    addEmailMutation.mutate(data);
  };

  const onBulkImportSubmit = (data: BulkImportFormValues) => {
    bulkImportMutation.mutate(data);
  };

  const handleDeleteEmail = (email: any) => {
    setSelectedEmail(email);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (selectedEmail) {
      deleteEmailMutation.mutate(selectedEmail.id);
    }
  };

  return (
    <AdminLayout>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Allowed Emails</h1>
          <p className="text-muted-foreground">Manage emails that are allowed to make purchases</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsBulkImportDialogOpen(true)} variant="outline">
            <Upload className="mr-2 h-4 w-4" /> Bulk Import
          </Button>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add Email
          </Button>
          <Button
            onClick={() => window.open('/api/export-allowed-emails', '_blank')}
            variant="outline"
          >
            <Download className="mr-2 h-4 w-4" /> Export to Excel
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      {emails && emails.length > 0 && (
        <div className="mb-4">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search emails, notes, or subjects..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          {searchQuery && (
            <p className="text-sm text-muted-foreground mt-2">
              Found {filteredEmails.length} result{filteredEmails.length !== 1 ? 's' : ''} for "{searchQuery}"
            </p>
          )}
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
        </div>
      ) : emails && emails.length > 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>Allowed Emails</CardTitle>
            <CardDescription>
              These emails are allowed to make purchases on checkout pages that require email validation.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredEmails.length === 0 && searchQuery ? (
              <div className="text-center py-8">
                <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No results found</h3>
                <p className="text-muted-foreground mb-4">
                  No emails match your search for "{searchQuery}". Try a different search term.
                </p>
                <Button variant="outline" onClick={() => setSearchQuery('')}>
                  Clear search
                </Button>
              </div>
            ) : (
              <div className="rounded-md border overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="py-3 px-4 text-left font-medium">Email</th>
                      <th className="py-3 px-4 text-left font-medium hidden md:table-cell">Last Subject</th>
                      <th className="py-3 px-4 text-left font-medium hidden md:table-cell">Notes</th>
                      <th className="py-3 px-4 text-left font-medium hidden lg:table-cell">Last Updated</th>
                      <th className="py-3 px-4 text-left font-medium hidden md:table-cell">Created</th>
                      <th className="py-3 px-4 text-right font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredEmails.map((email: any) => (
                    <tr key={email.id} className="border-b">
                      <td className="py-3 px-4">
                        <div>{email.email}</div>
                        <div className="text-xs text-muted-foreground md:hidden">
                          {email.lastSubject && <div>Subject: {email.lastSubject}</div>}
                          {email.lastUpdated && <div>Updated: {formatDate(email.lastUpdated)}</div>}
                        </div>
                      </td>
                      <td className="py-3 px-4 hidden md:table-cell">{email.lastSubject || '-'}</td>
                      <td className="py-3 px-4 hidden md:table-cell">
                        {email.notes ? (
                          <div className="whitespace-pre-line text-sm">{email.notes}</div>
                        ) : '-'}
                      </td>
                      <td className="py-3 px-4 hidden lg:table-cell">{email.lastUpdated ? formatDate(email.lastUpdated) : '-'}</td>
                      <td className="py-3 px-4 hidden md:table-cell">{formatDate(email.createdAt)}</td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex justify-end gap-1">
                          <QuickEmailActions
                            email={email.email}
                            lastSmtpProvider={email.smtpProvider}
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteEmail(email)}
                            className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
      ) : (
        <Card className="p-8 text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
            <Upload className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-lg font-semibold">No emails added yet</h3>
          <p className="mb-6 text-sm text-muted-foreground">
            Add emails that are allowed to make purchases on checkout pages that require email validation.
          </p>
          <div className="flex justify-center gap-2">
            <Button onClick={() => setIsBulkImportDialogOpen(true)} variant="outline">
              <Upload className="mr-2 h-4 w-4" /> Bulk Import
            </Button>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> Add Email
            </Button>
            <Button
              onClick={() => window.open('/api/export-allowed-emails', '_blank')}
              variant="outline"
            >
              <Download className="mr-2 h-4 w-4" /> Export to Excel
            </Button>
          </div>
        </Card>
      )}

      {/* Add Email Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Allowed Email</DialogTitle>
            <DialogDescription>
              Add a new email to the allowed list for checkout validation.
            </DialogDescription>
          </DialogHeader>

          <Form {...emailForm}>
            <form onSubmit={emailForm.handleSubmit(onAddSubmit)} className="space-y-4">
              <FormField
                control={emailForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={emailForm.control}
                name="lastSubject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Subject (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter last email subject" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={emailForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Add notes about this email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={addEmailMutation.isPending}>
                  {addEmailMutation.isPending ? 'Adding...' : 'Add Email'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Bulk Import Dialog */}
      <Dialog open={isBulkImportDialogOpen} onOpenChange={setIsBulkImportDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Bulk Import Emails</DialogTitle>
            <DialogDescription>
              Import multiple emails at once. Enter one email per line, or separate them with commas or spaces.
            </DialogDescription>
          </DialogHeader>

          <Form {...bulkImportForm}>
            <form onSubmit={bulkImportForm.handleSubmit(onBulkImportSubmit)} className="space-y-4">
              <FormField
                control={bulkImportForm.control}
                name="emails"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Emails</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter emails (one per line, or separated by commas or spaces)"
                        className="min-h-[200px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Example: <EMAIL>, <EMAIL>, <EMAIL>
                      <br />
                      Or: <EMAIL> <EMAIL> <EMAIL>
                      <br />
                      Or one email per line
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsBulkImportDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={bulkImportMutation.isPending}>
                  {bulkImportMutation.isPending ? 'Importing...' : 'Import Emails'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the email "{selectedEmail?.email}" from the allowed list.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleteDialogOpen(false)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {deleteEmailMutation.isPending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
}
