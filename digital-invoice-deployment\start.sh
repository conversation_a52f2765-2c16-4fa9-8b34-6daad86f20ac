#!/bin/bash

# CloudPanel.io Startup Script for Digital Invoice Application

echo "🚀 Starting Digital Invoice Application..."

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found, copying from .env.production"
    cp .env.production .env
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install --production
fi

# Start the application
echo "▶️  Starting application..."
npm start
