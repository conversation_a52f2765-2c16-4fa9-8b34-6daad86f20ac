import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { AlertTriangle, Send, CheckCircle, X } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

// Contact form schema
const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

interface ContactFormProps {
  userEmail?: string;
  checkoutPageSlug?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  isDialog?: boolean;
}

export function ContactForm({ userEmail, checkoutPageSlug, onSuccess, onCancel, isDialog = false }: ContactFormProps) {
  const [isSubmitted, setIsSubmitted] = useState(false);

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: '',
      email: userEmail || '',
      subject: 'Email not found - Need verification assistance',
      message: `Hi,\n\nI tried to access a checkout page but my email (${userEmail || 'my email'}) was not found in your database.\n\nTo verify my subscription, here is ONE of my verification details:\n\n• M3U Link: [Your playlist URL - if available]\n• Username: [Your service username - if available]\n• MAC Address: [Your device MAC address - if available]\n• PayPal Email: [Email used for payment - if available]\n\n(Please provide only ONE of the above that you have)\n\nPlease verify my account and contact me soon.\n\nThank you!`,
    },
  });

  const contactMutation = useMutation({
    mutationFn: async (data: ContactFormData) => {
      return apiRequest('/api/contact', 'POST', {
        ...data,
        checkoutPageSlug
      });
    },
    onSuccess: () => {
      setIsSubmitted(true);
      setTimeout(() => {
        onSuccess?.();
      }, 2000);
    },
    onError: (error: any) => {
      console.error('Contact form error:', error);
      form.setError('root', {
        message: 'Failed to send message. Please try again or contact support directly.',
      });
    },
  });

  const onSubmit = (data: ContactFormData) => {
    contactMutation.mutate(data);
  };

  if (isSubmitted) {
    return (
      <div className={`${isDialog ? 'p-6' : 'w-full max-w-md mx-auto'}`}>
        <div className="text-center space-y-4">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
          <div>
            <h3 className="text-lg font-semibold text-green-700">Message Sent!</h3>
            <p className="text-sm text-muted-foreground mt-2">
              Thank you for contacting us. We'll get back to you as soon as possible.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${isDialog ? 'p-2' : 'w-full max-w-md mx-auto'}`}>
      {/* Header */}
      <div className="text-center mb-4">
        <div className="flex items-center justify-center mb-2">
          <AlertTriangle className="h-6 w-6 text-amber-500 mr-2" />
          <h3 className="text-lg font-semibold">Email Not Found</h3>
        </div>
        <div className="text-sm text-muted-foreground space-y-2">
          <p>The email you entered is not in our database.</p>
          <p>This checkout page is only for existing subscribers.</p>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-left">
            <p className="font-medium text-blue-900 mb-1 text-xs">📋 To verify your subscription, please provide <strong>ONE</strong> of the following:</p>
            <ul className="text-blue-800 space-y-0.5 text-xs">
              <li>• <strong>M3U Link</strong> - Your playlist URL</li>
              <li>• <strong>Username</strong> - Your service username</li>
              <li>• <strong>MAC Address</strong> - Your device MAC address</li>
              <li>• <strong>PayPal Email</strong> - Email used for payment</li>
            </ul>
            <p className="text-blue-700 text-xs mt-1 font-medium">We will contact you soon after verification.</p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-left">
            <p className="font-medium text-green-900 mb-0.5 text-xs">🔄 Existing Customers:</p>
            <p className="text-green-800 text-xs">
              If you want to buy a second or third subscription, just use the same email from your first subscription since you already tested our service.
            </p>
          </div>

          <p className="font-medium text-xs">Contact us using the form below:</p>
        </div>
      </div>

      {/* Contact Form */}
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
        <div className="space-y-2">
          <Label htmlFor="name">Full Name</Label>
          <Input
            id="name"
            {...form.register('name')}
            placeholder="Your full name"
          />
          {form.formState.errors.name && (
            <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            {...form.register('email')}
            placeholder="Your Email"
          />
          {form.formState.errors.email && (
            <p className="text-sm text-red-500">{form.formState.errors.email.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="subject">Subject</Label>
          <Input
            id="subject"
            {...form.register('subject')}
            placeholder="Brief description of your issue"
          />
          {form.formState.errors.subject && (
            <p className="text-sm text-red-500">{form.formState.errors.subject.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="message">Message</Label>
          <Textarea
            id="message"
            {...form.register('message')}
            placeholder="To verify your subscription, please provide ONE of the following: M3U Link, Username, MAC Address, or PayPal Email. For existing customers buying additional subscriptions, use your original email from the first subscription."
            rows={4}
          />
          {form.formState.errors.message && (
            <p className="text-sm text-red-500">{form.formState.errors.message.message}</p>
          )}
        </div>

        {form.formState.errors.root && (
          <div className="text-sm text-red-500 text-center">
            {form.formState.errors.root.message}
          </div>
        )}

        <div className="pt-2">
          <Button
            type="submit"
            disabled={contactMutation.isPending}
            className="w-full"
          >
            {contactMutation.isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Sending...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Send Message
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
