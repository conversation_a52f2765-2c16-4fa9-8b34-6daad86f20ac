import { Router, Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { storage } from '../storage-factory';

export const generalSettingsRouter = Router();

// Session augmentation for TypeScript
declare module 'express-session' {
  interface SessionData {
    isAdmin: boolean;
    username: string;
    userId: number;
    rememberMe: boolean;
    requiresTwoFactor: boolean;
    twoFactorVerified: boolean;
  }
}

// Admin authentication middleware (same as in admin.ts)
const isAdmin = (req: Request, res: Response, next: NextFunction) => {
  console.log('Checking admin session for general settings:', req.session);

  // Check if user is authenticated and has completed 2FA if required
  if (req.session.isAdmin) {
    // If 2FA is required but not verified, deny access
    if (req.session.requiresTwoFactor && !req.session.twoFactorVerified) {
      console.log('2FA required but not verified');
      return res.status(401).json({
        message: 'Two-factor authentication required',
        requiresTwoFactor: true
      });
    }

    console.log('Admin session verified for general settings:', true);
    next();
  } else {
    console.log('Admin session verified for general settings:', false);
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Define the validation schema for general settings
const generalSettingsSchema = z.object({
  siteName: z.string().min(1, "Site name is required"),
  siteDescription: z.string().optional().default(""),
  logoUrl: z.string().optional().default(""),
  faviconUrl: z.string().optional().default(""),
  primaryColor: z.string().optional().default(""),
  secondaryColor: z.string().optional().default(""),
  footerText: z.string().optional().default(""),
  enableCheckout: z.boolean().default(true),
  enableCustomCheckout: z.boolean().default(true),
  enableTestMode: z.boolean().default(true),
  defaultTestCustomer: z.object({
    enabled: z.boolean().default(true),
    name: z.string().default(""),
    email: z.string().default("")
  }),
  emailDomainRestriction: z.object({
    enabled: z.boolean().default(true),
    allowedDomains: z.string().optional().default("")
  }),
  seoPrivacy: z.object({
    globalNoIndex: z.boolean().default(true),
    hideFromSearchEngines: z.boolean().default(true),
    disableSitemaps: z.boolean().default(true),
    hideFramework: z.boolean().default(true),
    customRobotsTxt: z.string().default(""),
    pageIndexingRules: z.object({
      homepage: z.boolean().default(false),
      checkoutPages: z.boolean().default(false),
      adminPages: z.boolean().default(false),
      customPages: z.boolean().default(false)
    }).default({
      homepage: false,
      checkoutPages: false,
      adminPages: false,
      customPages: false
    }),
    privacyHeaders: z.object({
      hideServerInfo: z.boolean().default(true),
      preventFraming: z.boolean().default(true),
      disableReferrer: z.boolean().default(true),
      hideGenerator: z.boolean().default(true)
    }).default({
      hideServerInfo: true,
      preventFraming: true,
      disableReferrer: true,
      hideGenerator: true
    })
  }).optional(),
  telegramBot: z.object({
    enabled: z.boolean().default(false),
    botToken: z.string().default(''),
    adminChatId: z.string().default(''),
    webhookUrl: z.string().default(''),
    notifications: z.object({
      newOrders: z.boolean().default(true),
      paymentConfirmations: z.boolean().default(true),
      trialUpgrades: z.boolean().default(true),
      orderStatusChanges: z.boolean().default(true)
    }).default({
      newOrders: true,
      paymentConfirmations: true,
      trialUpgrades: true,
      orderStatusChanges: true
    }),
    emailIntegration: z.object({
      enabled: z.boolean().default(true),
      allowQuickSend: z.boolean().default(true),
      defaultTemplateId: z.string().default('payment-confirmation')
    }).default({
      enabled: true,
      allowQuickSend: true,
      defaultTemplateId: 'payment-confirmation'
    }),
    m3uManagement: z.object({
      enabled: z.boolean().default(true),
      autoExtractCredentials: z.boolean().default(true),
      credentialFormat: z.string().default('Username: {username}\nPassword: {password}\nM3U URL: {m3u_url}'),
      defaultM3uLinks: z.array(z.string()).default([])
    }).default({
      enabled: true,
      autoExtractCredentials: true,
      credentialFormat: 'Username: {username}\nPassword: {password}\nM3U URL: {m3u_url}',
      defaultM3uLinks: []
    }),
    security: z.object({
      verifyAdminOnly: z.boolean().default(true),
      rateLimitEnabled: z.boolean().default(true),
      auditLogging: z.boolean().default(true)
    }).default({
      verifyAdminOnly: true,
      rateLimitEnabled: true,
      auditLogging: true
    })
  }).optional()
});

// Get general settings
generalSettingsRouter.get('/', isAdmin, async (req: Request, res: Response) => {
  try {
    const dbSettings = await storage.getGeneralSettings();

    if (!dbSettings) {
      // Return default settings if none exist
      return res.json({
        siteName: "TemplateHub Pro",
        siteDescription: "Premium productivity app templates and UI/UX design systems",
        logoUrl: "",
        faviconUrl: "",
        primaryColor: "#6366f1",
        secondaryColor: "#4f46e5",
        footerText: "© 2024 TemplateHub Pro",
        enableCheckout: true,
        enableCustomCheckout: true,
        enableTestMode: false,
        defaultTestCustomer: {
          enabled: false,
          name: "",
          email: ""
        },
        emailDomainRestriction: {
          enabled: false,
          allowedDomains: ""
        },
        seoPrivacy: {},
        telegramBot: {}
      });
    }

    // Convert database format to API format
    const settings = {
      siteName: dbSettings.siteName,
      siteDescription: dbSettings.siteDescription,
      logoUrl: dbSettings.logoUrl || "",
      faviconUrl: dbSettings.faviconUrl || "",
      primaryColor: dbSettings.primaryColor,
      secondaryColor: dbSettings.secondaryColor,
      footerText: dbSettings.footerText,
      enableCheckout: Boolean(dbSettings.enableCheckout),
      enableCustomCheckout: Boolean(dbSettings.enableCustomCheckout),
      enableTestMode: Boolean(dbSettings.enableTestMode),
      defaultTestCustomer: {
        enabled: Boolean(dbSettings.defaultTestCustomerEnabled),
        name: dbSettings.defaultTestCustomerName || "",
        email: dbSettings.defaultTestCustomerEmail || ""
      },
      emailDomainRestriction: {
        enabled: Boolean(dbSettings.emailDomainRestrictionEnabled),
        allowedDomains: dbSettings.emailDomainRestrictionDomains || ""
      },
      seoPrivacy: dbSettings.seoPrivacySettings ? JSON.parse(dbSettings.seoPrivacySettings) : {},
      telegramBot: dbSettings.telegramBotSettings ? JSON.parse(dbSettings.telegramBotSettings) : {}
    };

    res.json(settings);
  } catch (error) {
    console.error('Error fetching general settings:', error);
    res.status(500).json({ message: 'Failed to fetch general settings' });
  }
});

// Update general settings
generalSettingsRouter.put('/', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('PUT /api/general-settings called with body:', JSON.stringify(req.body, null, 2));

    // Validate the request body
    const validatedData = generalSettingsSchema.parse(req.body);
    console.log('Validation successful, validated data:', JSON.stringify(validatedData, null, 2));

    // Convert API format to database format
    const dbUpdate = {
      siteName: validatedData.siteName,
      siteDescription: validatedData.siteDescription || "",
      logoUrl: validatedData.logoUrl || null,
      faviconUrl: validatedData.faviconUrl || null,
      primaryColor: validatedData.primaryColor || "#6366f1",
      secondaryColor: validatedData.secondaryColor || "#4f46e5",
      footerText: validatedData.footerText || "",
      enableCheckout: validatedData.enableCheckout,
      enableCustomCheckout: validatedData.enableCustomCheckout,
      enableTestMode: validatedData.enableTestMode,
      defaultTestCustomerEnabled: validatedData.defaultTestCustomer?.enabled || false,
      defaultTestCustomerName: validatedData.defaultTestCustomer?.name || null,
      defaultTestCustomerEmail: validatedData.defaultTestCustomer?.email || null,
      emailDomainRestrictionEnabled: validatedData.emailDomainRestriction?.enabled || false,
      emailDomainRestrictionDomains: validatedData.emailDomainRestriction?.allowedDomains || null,
      seoPrivacySettings: validatedData.seoPrivacy ? JSON.stringify(validatedData.seoPrivacy) : null,
      telegramBotSettings: validatedData.telegramBot ? JSON.stringify(validatedData.telegramBot) : null,
      updatedAt: new Date().toISOString()
    };

    // Update the settings in database
    const updatedDbSettings = await storage.updateGeneralSettings(dbUpdate);
    console.log('Settings updated successfully in database');

    // Convert back to API format for response
    const responseSettings = {
      siteName: updatedDbSettings.siteName,
      siteDescription: updatedDbSettings.siteDescription,
      logoUrl: updatedDbSettings.logoUrl || "",
      faviconUrl: updatedDbSettings.faviconUrl || "",
      primaryColor: updatedDbSettings.primaryColor,
      secondaryColor: updatedDbSettings.secondaryColor,
      footerText: updatedDbSettings.footerText,
      enableCheckout: Boolean(updatedDbSettings.enableCheckout),
      enableCustomCheckout: Boolean(updatedDbSettings.enableCustomCheckout),
      enableTestMode: Boolean(updatedDbSettings.enableTestMode),
      defaultTestCustomer: {
        enabled: Boolean(updatedDbSettings.defaultTestCustomerEnabled),
        name: updatedDbSettings.defaultTestCustomerName || "",
        email: updatedDbSettings.defaultTestCustomerEmail || ""
      },
      emailDomainRestriction: {
        enabled: Boolean(updatedDbSettings.emailDomainRestrictionEnabled),
        allowedDomains: updatedDbSettings.emailDomainRestrictionDomains || ""
      },
      seoPrivacy: updatedDbSettings.seoPrivacySettings ? JSON.parse(updatedDbSettings.seoPrivacySettings) : {},
      telegramBot: updatedDbSettings.telegramBotSettings ? JSON.parse(updatedDbSettings.telegramBotSettings) : {}
    };

    // Sync with homepage configuration if site name or description changed
    if (validatedData.siteName || validatedData.siteDescription || validatedData.primaryColor || validatedData.secondaryColor) {
      try {
        const homepageConfig = await storage.getHomepageConfig();

        if (homepageConfig) {
          const currentSeo = JSON.parse(homepageConfig.seoSettings);
          const currentTheme = JSON.parse(homepageConfig.themeSettings);

          const updatedSeo = {
            ...currentSeo,
            title: validatedData.siteName || currentSeo.title,
            description: validatedData.siteDescription || currentSeo.description,
            ogTitle: validatedData.siteName || currentSeo.ogTitle,
            ogDescription: validatedData.siteDescription || currentSeo.ogDescription,
            twitterTitle: validatedData.siteName || currentSeo.twitterTitle,
            twitterDescription: validatedData.siteDescription || currentSeo.twitterDescription,
          };

          const updatedTheme = {
            ...currentTheme,
            primaryColor: validatedData.primaryColor || currentTheme.primaryColor,
            secondaryColor: validatedData.secondaryColor || currentTheme.secondaryColor,
          };

          await storage.updateHomepageConfig({
            seoSettings: JSON.stringify(updatedSeo),
            themeSettings: JSON.stringify(updatedTheme),
            version: homepageConfig.version + 1,
            updatedAt: new Date().toISOString()
          });

          console.log('Homepage configuration synced with general settings');
        }
      } catch (homepageError) {
        console.error('Error syncing homepage configuration:', homepageError);
        // Don't fail the general settings update if homepage sync fails
      }
    }

    res.json(responseSettings);
  } catch (error) {
    console.error('Error updating general settings:', error);

    if (error instanceof z.ZodError) {
      console.error('Validation errors:', error.errors);
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update general settings' });
  }
});

// Get allowed email domains
generalSettingsRouter.get('/allowed-email-domains', async (req: Request, res: Response) => {
  try {
    const dbSettings = await storage.getGeneralSettings();

    if (!dbSettings) {
      return res.json({ enabled: false, domains: [] });
    }

    const enabled = Boolean(dbSettings.emailDomainRestrictionEnabled);
    const allowedDomains = dbSettings.emailDomainRestrictionDomains || "";

    if (!enabled || !allowedDomains) {
      return res.json({ enabled, domains: [] });
    }

    const domains = allowedDomains
      .split(',')
      .map(domain => domain.trim())
      .filter(domain => domain.length > 0);

    res.json({ enabled, domains });
  } catch (error) {
    console.error('Error fetching allowed email domains:', error);
    res.status(500).json({ message: 'Failed to fetch allowed email domains' });
  }
});

// Get default test customer
generalSettingsRouter.get('/default-test-customer', async (req: Request, res: Response) => {
  try {
    const dbSettings = await storage.getGeneralSettings();

    if (!dbSettings) {
      return res.json({ enabled: false, name: "", email: "" });
    }

    const defaultTestCustomer = {
      enabled: Boolean(dbSettings.defaultTestCustomerEnabled),
      name: dbSettings.defaultTestCustomerName || "",
      email: dbSettings.defaultTestCustomerEmail || ""
    };

    res.json(defaultTestCustomer);
  } catch (error) {
    console.error('Error fetching default test customer:', error);
    res.status(500).json({ message: 'Failed to fetch default test customer' });
  }
});
