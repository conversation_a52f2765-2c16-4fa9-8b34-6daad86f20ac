import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight,
  List, ListOrdered, Link, Image, Code, Eye, Save, Undo, Redo,
  Type, Heading1, Heading2, Heading3, Palette, FileText, Table,
  Quote, Minus, Plus, ChevronDown
} from 'lucide-react';

interface AdvancedEmailEditorProps {
  initialValue: string;
  onChange: (content: string) => void;
  height?: number;
  placeholder?: string;
  label?: string;
  helpText?: string;
  showPreview?: boolean;
}

const AdvancedEmailEditor: React.FC<AdvancedEmailEditorProps> = ({
  initialValue,
  onChange,
  height = 500,
  placeholder = 'Start typing...',
  label,
  helpText,
  showPreview = true,
}) => {
  const [content, setContent] = useState(initialValue);
  const [activeTab, setActiveTab] = useState<string>('visual');
  const [fontSize, setFontSize] = useState('14');
  const [fontFamily, setFontFamily] = useState('Arial');
  const [textColor, setTextColor] = useState('#000000');
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [linkUrl, setLinkUrl] = useState('');
  const [linkText, setLinkText] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [isLinkPopoverOpen, setIsLinkPopoverOpen] = useState(false);
  const [isImagePopoverOpen, setIsImagePopoverOpen] = useState(false);
  const [isColorPopoverOpen, setIsColorPopoverOpen] = useState(false);
  const editorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setContent(initialValue);
  }, [initialValue]);

  const handleCodeChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setContent(newContent);
    onChange(newContent);
  };

  const execCommand = (command: string, value: string = '') => {
    document.execCommand('styleWithCSS', false, 'true');
    document.execCommand(command, false, value);
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      setContent(newContent);
      onChange(newContent);
    }
  };

  const insertLink = () => {
    if (linkUrl && linkText) {
      const linkHtml = `<a href="${linkUrl}" target="_blank">${linkText}</a>`;
      document.execCommand('insertHTML', false, linkHtml);
      if (editorRef.current) {
        const newContent = editorRef.current.innerHTML;
        setContent(newContent);
        onChange(newContent);
      }
      setLinkUrl('');
      setLinkText('');
      setIsLinkPopoverOpen(false);
    }
  };

  const insertImage = () => {
    if (imageUrl) {
      const imgHtml = `<img src="${imageUrl}" alt="${imageAlt}" style="max-width: 100%; height: auto;" />`;
      document.execCommand('insertHTML', false, imgHtml);
      if (editorRef.current) {
        const newContent = editorRef.current.innerHTML;
        setContent(newContent);
        onChange(newContent);
      }
      setImageUrl('');
      setImageAlt('');
      setIsImagePopoverOpen(false);
    }
  };

  const insertTable = () => {
    const tableHtml = `
      <table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">
        <tr>
          <th style="padding: 8px; background-color: #f5f5f5;">Header 1</th>
          <th style="padding: 8px; background-color: #f5f5f5;">Header 2</th>
        </tr>
        <tr>
          <td style="padding: 8px;">Cell 1</td>
          <td style="padding: 8px;">Cell 2</td>
        </tr>
      </table>
    `;
    document.execCommand('insertHTML', false, tableHtml);
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      setContent(newContent);
      onChange(newContent);
    }
  };

  const applyFontSize = () => {
    execCommand('fontSize', '3');
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const span = document.createElement('span');
      span.style.fontSize = fontSize + 'px';
      try {
        range.surroundContents(span);
      } catch (e) {
        span.appendChild(range.extractContents());
        range.insertNode(span);
      }
      if (editorRef.current) {
        const newContent = editorRef.current.innerHTML;
        setContent(newContent);
        onChange(newContent);
      }
    }
  };

  const applyFontFamily = () => {
    execCommand('fontName', fontFamily);
  };

  const applyTextColor = () => {
    execCommand('foreColor', textColor);
  };

  const applyBackgroundColor = () => {
    execCommand('backColor', backgroundColor);
  };

  const insertVariable = (variable: string) => {
    if (editorRef.current) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        if (editorRef.current.contains(range.commonAncestorContainer)) {
          const textNode = document.createTextNode(variable);
          range.deleteContents();
          range.insertNode(textNode);

          const newContent = editorRef.current.innerHTML;
          setContent(newContent);
          onChange(newContent);

          range.setStartAfter(textNode);
          range.setEndAfter(textNode);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      }
    }
  };

  const insertTemplate = (template: string) => {
    if (editorRef.current) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        if (editorRef.current.contains(range.commonAncestorContainer)) {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = template;

          range.deleteContents();

          const fragment = document.createDocumentFragment();
          while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
          }

          range.insertNode(fragment);

          const newContent = editorRef.current.innerHTML;
          setContent(newContent);
          onChange(newContent);
        }
      }
    }
  };

  return (
    <div className="space-y-2">
      {label && <div className="text-sm font-medium">{label}</div>}
      {helpText && <div className="text-sm text-muted-foreground mb-2">{helpText}</div>}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="visual" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            <span>Visual Editor</span>
          </TabsTrigger>
          <TabsTrigger value="code" className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            <span>HTML Code</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="visual" className="border rounded-md mt-2">
          {/* Advanced Toolbar */}
          <div className="border-b p-3 space-y-2">
            {/* First Row - Text Formatting */}
            <div className="flex flex-wrap gap-1 items-center">
              <div className="flex items-center gap-1 mr-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('formatBlock', '<h1>')}
                  title="Heading 1"
                >
                  <Heading1 className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('formatBlock', '<h2>')}
                  title="Heading 2"
                >
                  <Heading2 className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('formatBlock', '<h3>')}
                  title="Heading 3"
                >
                  <Heading3 className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('formatBlock', '<p>')}
                  title="Paragraph"
                >
                  <Type className="h-4 w-4" />
                </Button>
              </div>

              <div className="w-px h-6 bg-border mx-1" />

              <div className="flex items-center gap-1 mr-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('bold')}
                  title="Bold"
                >
                  <Bold className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('italic')}
                  title="Italic"
                >
                  <Italic className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('underline')}
                  title="Underline"
                >
                  <Underline className="h-4 w-4" />
                </Button>
              </div>

              <div className="w-px h-6 bg-border mx-1" />

              <div className="flex items-center gap-1 mr-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('justifyLeft')}
                  title="Align Left"
                >
                  <AlignLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('justifyCenter')}
                  title="Align Center"
                >
                  <AlignCenter className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('justifyRight')}
                  title="Align Right"
                >
                  <AlignRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="w-px h-6 bg-border mx-1" />

              <div className="flex items-center gap-1 mr-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('insertUnorderedList')}
                  title="Bullet List"
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => execCommand('insertOrderedList')}
                  title="Numbered List"
                >
                  <ListOrdered className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Second Row - Advanced Features */}
            <div className="flex flex-wrap gap-1 items-center">
              {/* Font Controls */}
              <div className="flex items-center gap-2 mr-2">
                <Select value={fontFamily} onValueChange={setFontFamily}>
                  <SelectTrigger className="w-32 h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Arial">Arial</SelectItem>
                    <SelectItem value="Helvetica">Helvetica</SelectItem>
                    <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                    <SelectItem value="Georgia">Georgia</SelectItem>
                    <SelectItem value="Verdana">Verdana</SelectItem>
                    <SelectItem value="Courier New">Courier New</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={applyFontFamily}
                  title="Apply Font"
                >
                  Apply
                </Button>
              </div>

              <div className="flex items-center gap-2 mr-2">
                <Select value={fontSize} onValueChange={setFontSize}>
                  <SelectTrigger className="w-20 h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10px</SelectItem>
                    <SelectItem value="12">12px</SelectItem>
                    <SelectItem value="14">14px</SelectItem>
                    <SelectItem value="16">16px</SelectItem>
                    <SelectItem value="18">18px</SelectItem>
                    <SelectItem value="20">20px</SelectItem>
                    <SelectItem value="24">24px</SelectItem>
                    <SelectItem value="28">28px</SelectItem>
                    <SelectItem value="32">32px</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={applyFontSize}
                  title="Apply Font Size"
                >
                  Apply
                </Button>
              </div>

              <div className="w-px h-6 bg-border mx-1" />

              {/* Color Controls */}
              <Popover open={isColorPopoverOpen} onOpenChange={setIsColorPopoverOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    title="Text & Background Color"
                  >
                    <Palette className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-64">
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label htmlFor="textColor">Text Color</Label>
                      <div className="flex gap-2">
                        <Input
                          id="textColor"
                          type="color"
                          value={textColor}
                          onChange={(e) => setTextColor(e.target.value)}
                          className="w-16 h-8"
                        />
                        <Button
                          size="sm"
                          onClick={applyTextColor}
                        >
                          Apply Text Color
                        </Button>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="backgroundColor">Background Color</Label>
                      <div className="flex gap-2">
                        <Input
                          id="backgroundColor"
                          type="color"
                          value={backgroundColor}
                          onChange={(e) => setBackgroundColor(e.target.value)}
                          className="w-16 h-8"
                        />
                        <Button
                          size="sm"
                          onClick={applyBackgroundColor}
                        >
                          Apply Background
                        </Button>
                      </div>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              <div className="w-px h-6 bg-border mx-1" />

              {/* Insert Controls */}
              <Popover open={isLinkPopoverOpen} onOpenChange={setIsLinkPopoverOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    title="Insert Link"
                  >
                    <Link className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label htmlFor="linkText">Link Text</Label>
                      <Input
                        id="linkText"
                        value={linkText}
                        onChange={(e) => setLinkText(e.target.value)}
                        placeholder="Enter link text"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="linkUrl">URL</Label>
                      <Input
                        id="linkUrl"
                        value={linkUrl}
                        onChange={(e) => setLinkUrl(e.target.value)}
                        placeholder="https://example.com"
                      />
                    </div>
                    <Button onClick={insertLink} className="w-full">
                      Insert Link
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>

              <Popover open={isImagePopoverOpen} onOpenChange={setIsImagePopoverOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    title="Insert Image"
                  >
                    <Image className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label htmlFor="imageUrl">Image URL</Label>
                      <Input
                        id="imageUrl"
                        value={imageUrl}
                        onChange={(e) => setImageUrl(e.target.value)}
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="imageAlt">Alt Text</Label>
                      <Input
                        id="imageAlt"
                        value={imageAlt}
                        onChange={(e) => setImageAlt(e.target.value)}
                        placeholder="Image description"
                      />
                    </div>
                    <Button onClick={insertImage} className="w-full">
                      Insert Image
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>

              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={insertTable}
                title="Insert Table"
              >
                <Table className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('formatBlock', '<blockquote>')}
                title="Quote"
              >
                <Quote className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('insertHorizontalRule')}
                title="Horizontal Line"
              >
                <Minus className="h-4 w-4" />
              </Button>

              <div className="w-px h-6 bg-border mx-1" />

              {/* Undo/Redo */}
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('undo')}
                title="Undo"
              >
                <Undo className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('redo')}
                title="Redo"
              >
                <Redo className="h-4 w-4" />
              </Button>
            </div>

            {/* Third Row - Templates and Variables */}
            <div className="flex flex-wrap gap-1 items-center">
              <div className="flex items-center gap-2">
                <Label className="text-xs">Quick Insert:</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => insertVariable('{{customerName}}')}
                >
                  Customer Name
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => insertVariable('{{customerEmail}}')}
                >
                  Customer Email
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => insertVariable('{{orderId}}')}
                >
                  Order ID
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => insertVariable('{{amount}}')}
                >
                  Amount
                </Button>
              </div>

              <div className="w-px h-6 bg-border mx-1" />

              <div className="flex items-center gap-2">
                <Select onValueChange={(value) => insertTemplate(value)}>
                  <SelectTrigger className="w-48 h-8">
                    <SelectValue placeholder="Insert Template" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='<p>Dear {{customerName}},</p><p>Thank you for your order!</p>'>
                      Welcome Message
                    </SelectItem>
                    <SelectItem value='<table border="1" style="border-collapse: collapse; width: 100%;"><tr><th style="padding: 8px;">Item</th><th style="padding: 8px;">Amount</th></tr><tr><td style="padding: 8px;">{{productName}}</td><td style="padding: 8px;">{{amount}}</td></tr></table>'>
                      Order Summary Table
                    </SelectItem>
                    <SelectItem value='<p>Lien: https://example.com/your-link</p>'>
                      Link Placeholder
                    </SelectItem>
                    <SelectItem value='<div style="background-color: #f8f9fa; padding: 20px; border-left: 4px solid #007bff; margin: 20px 0;"><p><strong>Important:</strong> Please save this information for your records.</p></div>'>
                      Important Notice Box
                    </SelectItem>
                    <SelectItem value='<div style="text-align: center; padding: 20px; background-color: #e9ecef; border-radius: 8px;"><h3>Contact Support</h3><p>Email: <EMAIL></p><p>Phone: +****************</p></div>'>
                      Contact Information
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Editor Content Area */}
          <div
            ref={editorRef}
            className="p-4 min-h-[300px] focus:outline-none"
            style={{ height: `${height - 150}px`, overflowY: 'auto' }}
            contentEditable
            dangerouslySetInnerHTML={{ __html: content }}
            onInput={(e) => {
              const newContent = (e.target as HTMLDivElement).innerHTML;
              setContent(newContent);
              onChange(newContent);
            }}
            onBlur={(e) => {
              const newContent = (e.target as HTMLDivElement).innerHTML;
              setContent(newContent);
              onChange(newContent);
            }}
          />
        </TabsContent>

        <TabsContent value="code" className="border rounded-md mt-2 p-0">
          <textarea
            value={content}
            onChange={handleCodeChange}
            className="w-full p-4 font-mono text-sm resize-none focus:outline-none"
            style={{ height: `${height}px` }}
            placeholder="Enter HTML code here..."
          />
        </TabsContent>
      </Tabs>

      {showPreview && (
        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">Preview</h3>
          <Card>
            <CardContent className="p-4">
              <div
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ __html: content }}
              />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AdvancedEmailEditor;
