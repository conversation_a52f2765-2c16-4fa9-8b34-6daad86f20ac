import { useState } from 'react';
import { format } from 'date-fns';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface OrderDetailsProps {
  order: any;
  onClose: () => void;
  onOrderUpdated?: () => void;
}

export default function OrderDetails({ order, onClose, onOrderUpdated }: OrderDetailsProps) {
  const [editedOrder, setEditedOrder] = useState<any>(order);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (e) {
      return 'Invalid date';
    }
  };

  // Handle order update
  const handleUpdateOrder = async () => {
    try {
      console.log('Updating order:', editedOrder);

      // Send the updated order to the server
      const response = await fetch(`/api/admin/invoices/${editedOrder.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerName: editedOrder.customerName,
          customerEmail: editedOrder.customerEmail,
          status: editedOrder.status,
          notes: editedOrder.notes
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update order');
      }

      const result = await response.json();
      console.log('Order updated successfully:', result);

      // Show success message
      alert('Order updated successfully!');

      // Call the onOrderUpdated callback if provided
      if (onOrderUpdated) {
        onOrderUpdated();
      }

      // Close the dialog
      onClose();
    } catch (error) {
      console.error('Error updating order:', error);
      alert('Failed to update order. Please try again.');
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="details">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="details">Order Details</TabsTrigger>
          <TabsTrigger value="customer">Customer Information</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4 pt-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Order ID</Label>
              <div className="font-medium">{order.id}</div>
            </div>
            <div>
              <Label>Status</Label>
              <div>
                <Select
                  value={editedOrder.status}
                  onValueChange={(value) => setEditedOrder({...editedOrder, status: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="sent">Sent</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="no_paypal">No PayPal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label>Created Date</Label>
              <div>{formatDate(order.createdAt)}</div>
            </div>
            <div>
              <Label>Product</Label>
              <div className="font-medium">
                {order.productName || `Product #${order.productId}`}
              </div>
            </div>
            {order.checkoutPageTitle && (
              <div className="col-span-2">
                <Label>Checkout Page</Label>
                <div className="flex items-center">
                  <div className="font-medium text-blue-600">{order.checkoutPageTitle}</div>
                  {order.checkoutPageId && (
                    <div className="text-xs text-muted-foreground ml-2">(ID: {order.checkoutPageId})</div>
                  )}
                </div>
              </div>
            )}
            <div>
              <Label>Country</Label>
              <div className="font-medium">
                {order.country || 'Not specified'}
              </div>
            </div>
            <div>
              <Label>Application</Label>
              <div className="font-medium">
                {order.appType || 'Not specified'}
              </div>
            </div>
            {order.macAddress && (
              <div>
                <Label>MAC Address</Label>
                <div className="font-medium">
                  {order.macAddress}
                </div>
              </div>
            )}
            <div className="col-span-2">
              <Label>Notes</Label>
              <Textarea
                value={editedOrder.notes || ''}
                onChange={(e) => setEditedOrder({...editedOrder, notes: e.target.value})}
                className="mt-1"
                rows={3}
              />
            </div>
          </div>

          <div className="flex justify-between pt-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button onClick={handleUpdateOrder}>
              Save Changes
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="customer" className="space-y-4 pt-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Customer Name</Label>
              <Input
                value={editedOrder.customerName || ''}
                onChange={(e) => setEditedOrder({...editedOrder, customerName: e.target.value})}
                className="mt-1"
              />
            </div>
            <div>
              <Label>Customer Email</Label>
              <Input
                value={editedOrder.customerEmail || ''}
                onChange={(e) => setEditedOrder({...editedOrder, customerEmail: e.target.value})}
                className="mt-1"
              />
            </div>
          </div>

          <div className="flex justify-between pt-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button onClick={handleUpdateOrder}>
              Save Changes
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
