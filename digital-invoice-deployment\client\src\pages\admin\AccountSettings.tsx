import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useAdminAuth } from '@/hooks/use-admin-auth';
import { Loader2 } from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';
import TwoFactorSetup from '@/components/admin/TwoFactorSetup';
import DeviceManager from '@/components/admin/DeviceManager';
import RecoveryCodes from '@/components/admin/RecoveryCodes';

// Username validation schema
const usernameSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newUsername: z.string().min(3, 'Username must be at least 3 characters'),
});

// Password validation schema
const passwordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Auto-login settings schema
const autoLoginSchema = z.object({
  rememberMe: z.boolean().default(false),
});

type UsernameFormData = z.infer<typeof usernameSchema>;
type PasswordFormData = z.infer<typeof passwordSchema>;
type AutoLoginFormData = z.infer<typeof autoLoginSchema>;

export default function AccountSettings() {
  const { toast } = useToast();
  const { isAuthenticated, checkAuth } = useAdminAuth();
  const [activeTab, setActiveTab] = useState('username');
  const [isSubmittingUsername, setIsSubmittingUsername] = useState(false);
  const [isSubmittingPassword, setIsSubmittingPassword] = useState(false);
  const [isSubmittingAutoLogin, setIsSubmittingAutoLogin] = useState(false);

  // Username form setup
  const usernameForm = useForm<UsernameFormData>({
    resolver: zodResolver(usernameSchema),
    defaultValues: {
      currentPassword: '',
      newUsername: '',
    },
  });

  // Password form setup
  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  // Auto-login form setup
  const autoLoginForm = useForm<AutoLoginFormData>({
    resolver: zodResolver(autoLoginSchema),
    defaultValues: {
      rememberMe: false,
    },
  });

  // Load auto-login settings
  React.useEffect(() => {
    const loadAutoLoginSettings = async () => {
      try {
        const response = await apiRequest('/api/admin/auto-login-settings', 'GET');
        autoLoginForm.setValue('rememberMe', response.rememberMe || false);
      } catch (error) {
        console.error('Error loading auto-login settings:', error);
      }
    };

    if (isAuthenticated) {
      loadAutoLoginSettings();
    }
  }, [isAuthenticated, autoLoginForm]);

  // Username submit handler
  const onSubmitUsername = async (data: UsernameFormData) => {
    setIsSubmittingUsername(true);
    try {
      await apiRequest('/api/admin/change-username', 'POST', data);
      toast({
        title: "Username updated",
        description: "Your username has been updated successfully.",
      });
      usernameForm.reset();
      checkAuth(); // Refresh auth state
    } catch (error) {
      toast({
        title: "Failed to update username",
        description: error instanceof Error ? error.message : "An error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmittingUsername(false);
    }
  };

  // Password submit handler
  const onSubmitPassword = async (data: PasswordFormData) => {
    setIsSubmittingPassword(true);
    try {
      await apiRequest('/api/admin/change-password', 'POST', {
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      });
      toast({
        title: "Password updated",
        description: "Your password has been updated successfully.",
      });
      passwordForm.reset();
    } catch (error) {
      toast({
        title: "Failed to update password",
        description: error instanceof Error ? error.message : "An error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmittingPassword(false);
    }
  };

  // Auto-login submit handler
  const onSubmitAutoLogin = async (data: AutoLoginFormData) => {
    setIsSubmittingAutoLogin(true);
    try {
      await apiRequest('/api/admin/auto-login-settings', 'POST', data);
      toast({
        title: "Settings updated",
        description: "Your auto-login settings have been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Failed to update settings",
        description: error instanceof Error ? error.message : "An error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmittingAutoLogin(false);
    }
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">Account Settings</h1>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="username">Change Username</TabsTrigger>
            <TabsTrigger value="password">Change Password</TabsTrigger>
            <TabsTrigger value="auto-login">Auto-Login Settings</TabsTrigger>
            <TabsTrigger value="two-factor">Two-Factor Authentication</TabsTrigger>
            <TabsTrigger value="recovery-codes">Recovery Codes</TabsTrigger>
            <TabsTrigger value="devices">Device Manager</TabsTrigger>
          </TabsList>

          <TabsContent value="username">
            <Card>
              <CardHeader>
                <CardTitle>Change Username</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...usernameForm}>
                  <form onSubmit={usernameForm.handleSubmit(onSubmitUsername)} className="space-y-6">
                    <FormField
                      control={usernameForm.control}
                      name="currentPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Current Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={usernameForm.control}
                      name="newUsername"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>New Username</FormLabel>
                          <FormControl>
                            <Input placeholder="New username" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button
                      type="submit"
                      disabled={isSubmittingUsername}
                    >
                      {isSubmittingUsername ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        "Update Username"
                      )}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="password">
            <Card>
              <CardHeader>
                <CardTitle>Change Password</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...passwordForm}>
                  <form onSubmit={passwordForm.handleSubmit(onSubmitPassword)} className="space-y-6">
                    <FormField
                      control={passwordForm.control}
                      name="currentPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Current Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={passwordForm.control}
                      name="newPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>New Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={passwordForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Confirm New Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button
                      type="submit"
                      disabled={isSubmittingPassword}
                    >
                      {isSubmittingPassword ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        "Update Password"
                      )}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="auto-login">
            <Card>
              <CardHeader>
                <CardTitle>Auto-Login Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...autoLoginForm}>
                  <form onSubmit={autoLoginForm.handleSubmit(onSubmitAutoLogin)} className="space-y-6">
                    <FormField
                      control={autoLoginForm.control}
                      name="rememberMe"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Remember me on this device
                            </FormLabel>
                            <p className="text-sm text-muted-foreground">
                              When enabled, you'll stay logged in on this device for 30 days.
                            </p>
                          </div>
                        </FormItem>
                      )}
                    />
                    <Button
                      type="submit"
                      disabled={isSubmittingAutoLogin}
                    >
                      {isSubmittingAutoLogin ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Save Settings"
                      )}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="two-factor">
            <TwoFactorSetup />
          </TabsContent>

          <TabsContent value="recovery-codes">
            <RecoveryCodes />
          </TabsContent>

          <TabsContent value="devices">
            <DeviceManager />
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
