import React, { useState } from 'react';
import { Button } from '@/components/ui/button';

interface TextareaEmailEditorProps {
  value: string;
  onChange: (value: string) => void;
}

const TextareaEmailEditor: React.FC<TextareaEmailEditorProps> = ({ value, onChange }) => {
  const [showPreview, setShowPreview] = useState<boolean>(false);

  // Insert text at cursor position
  const insertAtCursor = (textToInsert: string) => {
    const textarea = document.getElementById('email-textarea') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;
    const before = text.substring(0, start);
    const after = text.substring(end);

    const newText = before + textToInsert + after;
    onChange(newText);

    // Set cursor position after the inserted text
    setTimeout(() => {
      textarea.focus();
      textarea.selectionStart = start + textToInsert.length;
      textarea.selectionEnd = start + textToInsert.length;
    }, 0);
  };

  // Insert formatting tags
  const insertFormatting = (tag: string) => {
    const textarea = document.getElementById('email-textarea') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);

    let textToInsert = '';
    switch (tag) {
      case 'bold':
        textToInsert = `<strong>${selectedText}</strong>`;
        break;
      case 'italic':
        textToInsert = `<em>${selectedText}</em>`;
        break;
      case 'underline':
        textToInsert = `<u>${selectedText}</u>`;
        break;
      case 'link':
        const url = prompt('Enter URL:', 'https://');
        if (url) {
          textToInsert = `<a href="${url}">${selectedText || url}</a>`;
        } else {
          return;
        }
        break;
      case 'username':
        const username = prompt('Enter username:');
        if (username) {
          textToInsert = `Username: ${username}`;
        } else {
          return;
        }
        break;
      case 'list':
        if (selectedText) {
          const items = selectedText.split('\n').filter(item => item.trim());
          textToInsert = '<ul>\n' + items.map(item => `  <li>${item}</li>`).join('\n') + '\n</ul>';
        } else {
          textToInsert = '<ul>\n  <li>Item 1</li>\n  <li>Item 2</li>\n  <li>Item 3</li>\n</ul>';
        }
        break;
      default:
        return;
    }

    const text = textarea.value;
    const before = text.substring(0, start);
    const after = text.substring(end);

    const newText = before + textToInsert + after;
    onChange(newText);

    // Set cursor position after the inserted text
    setTimeout(() => {
      textarea.focus();
      textarea.selectionStart = start + textToInsert.length;
      textarea.selectionEnd = start + textToInsert.length;
    }, 0);
  };

  return (
    <div className="border rounded-md overflow-hidden">
      {/* Toolbar */}
      <div className="bg-muted p-2 flex flex-wrap gap-1 border-b">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertFormatting('bold')}
          title="Bold"
        >
          Bold
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertFormatting('italic')}
          title="Italic"
        >
          Italic
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertFormatting('underline')}
          title="Underline"
        >
          Underline
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertFormatting('list')}
          title="List"
        >
          List
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertFormatting('link')}
          title="Link"
        >
          Link
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => insertFormatting('username')}
          title="Username"
        >
          Username
        </Button>
        <div className="ml-auto">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? 'Edit' : 'Preview'}
          </Button>
        </div>
      </div>

      {showPreview ? (
        <div
          className="w-full min-h-[300px] p-4 overflow-auto"
          style={{ minHeight: '300px' }}
          dangerouslySetInnerHTML={{ __html: value }}
        />
      ) : (
        <textarea
          id="email-textarea"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-full min-h-[300px] p-4 focus:outline-none resize-none"
          style={{
            minHeight: '300px',
            direction: 'ltr',
            textAlign: 'left'
          }}
        />
      )}
    </div>
  );
};

export default TextareaEmailEditor;
