import { apiRequest } from '@/lib/queryClient';
import { SystemMessage } from '@/lib/system-messages';

// API functions
export const getSystemMessages = async (): Promise<SystemMessage[]> => {
  return apiRequest('/api/system-messages', 'GET');
};

export const getSystemMessage = async (id: string): Promise<SystemMessage> => {
  return apiRequest(`/api/system-messages/${id}`, 'GET');
};

export const updateSystemMessage = async (id: string, data: Partial<SystemMessage>): Promise<SystemMessage> => {
  return apiRequest(`/api/system-messages/${id}`, 'PUT', data);
};

// Helper function to get system messages by category
export const getSystemMessagesByCategory = async (category: string): Promise<SystemMessage[]> => {
  const messages = await getSystemMessages();
  return messages.filter(msg => msg.category === category);
};
