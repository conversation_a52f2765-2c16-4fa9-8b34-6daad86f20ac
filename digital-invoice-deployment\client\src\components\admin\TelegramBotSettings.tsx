import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Bot, MessageSquare, Mail, Link, Shield, TestTube, RefreshCw, Trash2, Info } from 'lucide-react';

interface TelegramConfig {
  enabled: boolean;
  botToken: string;
  adminChatId: string;
  webhookUrl: string;
  notifications: {
    newOrders: boolean;
    paymentConfirmations: boolean;
    trialUpgrades: boolean;
    orderStatusChanges: boolean;
  };
  emailIntegration: {
    enabled: boolean;
    allowQuickSend: boolean;
    defaultTemplateId: string;
  };
  m3uManagement: {
    enabled: boolean;
    autoExtractCredentials: boolean;
    credentialFormat: string;
    defaultM3uLinks: string[];
  };
  security: {
    verifyAdminOnly: boolean;
    rateLimitEnabled: boolean;
    auditLogging: boolean;
  };
}

const TelegramBotSettings: React.FC = () => {
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  const [webhookInfo, setWebhookInfo] = useState<any>(null);

  const { data: config, isLoading } = useQuery({
    queryKey: ['telegramConfig'],
    queryFn: async () => {
      const response = await fetch('/api/telegram/config', {
        credentials: 'include'
      });
      if (!response.ok) throw new Error('Failed to fetch config');
      return response.json();
    },
  });

  const [formData, setFormData] = useState<TelegramConfig>({
    enabled: false,
    botToken: '',
    adminChatId: '',
    webhookUrl: '',
    notifications: {
      newOrders: true,
      paymentConfirmations: true,
      trialUpgrades: true,
      orderStatusChanges: true,
    },
    emailIntegration: {
      enabled: true,
      allowQuickSend: true,
      defaultTemplateId: 'payment-confirmation',
    },
    m3uManagement: {
      enabled: true,
      autoExtractCredentials: true,
      credentialFormat: 'Username: {username}\nPassword: {password}\nM3U URL: {m3u_url}',
      defaultM3uLinks: [],
    },
    security: {
      verifyAdminOnly: true,
      rateLimitEnabled: true,
      auditLogging: true,
    },
  });

  React.useEffect(() => {
    if (config) {
      setFormData(config);
    }
  }, [config]);

  // Load webhook info when component mounts or when bot token changes
  React.useEffect(() => {
    if (formData.botToken) {
      webhookInfoMutation.mutate();
    }
  }, [formData.botToken]);

  const updateMutation = useMutation({
    mutationFn: async (data: TelegramConfig) => {
      const response = await fetch('/api/telegram/config', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error('Failed to update config');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['telegramConfig'] });
      setIsEditing(false);
    },
  });

  const testConnectionMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/telegram/test-connection', {
        method: 'POST',
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to test connection');
      return response.json();
    },
    onSuccess: (data) => {
      setTestResults(data);
    },
  });

  const testNotificationMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/telegram/test-notification', {
        method: 'POST',
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to send test notification');
      return response.json();
    },
    onSuccess: (data) => {
      setTestResults(data);
    },
  });

  const setWebhookMutation = useMutation({
    mutationFn: async () => {
      const webhookUrl = formData.webhookUrl || `${window.location.origin}/api/telegram/webhook`;
      const response = await fetch('/api/telegram/set-webhook', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ webhookUrl }),
      });
      if (!response.ok) throw new Error('Failed to set webhook');
      return response.json();
    },
    onSuccess: (data) => {
      setTestResults(data);
      // Update webhook URL in form if it was successful
      if (data.success) {
        const webhookUrl = formData.webhookUrl || `${window.location.origin}/api/telegram/webhook`;
        setFormData(prev => ({
          ...prev,
          webhookUrl: webhookUrl
        }));
      }
      // Refresh webhook info after setting
      webhookInfoMutation.mutate();
    },
  });

  const removeWebhookMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/telegram/remove-webhook', {
        method: 'POST',
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to remove webhook');
      return response.json();
    },
    onSuccess: (data) => {
      setTestResults(data);
      // Clear webhook URL from form if removal was successful
      if (data.success) {
        setFormData(prev => ({
          ...prev,
          webhookUrl: ''
        }));
      }
      // Refresh webhook info after removal
      webhookInfoMutation.mutate();
    },
  });

  const webhookInfoMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/telegram/webhook-info', {
        method: 'GET',
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to get webhook info');
      return response.json();
    },
    onSuccess: (data) => {
      setWebhookInfo(data.webhookInfo);
    },
  });

  const resetCacheMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/telegram/reset-cache', {
        method: 'POST',
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to reset cache');
      return response.json();
    },
    onSuccess: (data) => {
      setTestResults(data);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateMutation.mutate(formData);
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNestedChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof typeof prev],
        [field]: value,
      },
    }));
  };

  if (isLoading) {
    return <div className="p-6">Loading Telegram Bot settings...</div>;
  }

  return (
    <div className="p-6 max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Bot className="h-8 w-8 text-blue-600" />
              <div>
                <CardTitle className="text-2xl">Telegram Bot Settings</CardTitle>
                <CardDescription>
                  Configure your Telegram bot for order notifications and management
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={formData.enabled ? "default" : "secondary"}>
                {formData.enabled ? "Enabled" : "Disabled"}
              </Badge>
              <Button
                onClick={() => setIsEditing(!isEditing)}
                variant={isEditing ? "outline" : "default"}
              >
                {isEditing ? 'Cancel' : 'Edit Settings'}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Setup Instructions */}
      {!formData.botToken && (
        <Alert>
          <Bot className="h-4 w-4" />
          <AlertDescription>
            <strong>Quick Setup:</strong> 1) Message @BotFather on Telegram → 2) Send /newbot → 3) Choose a name → 4) Copy the bot token below → 5) Start your bot and get your Chat ID
          </AlertDescription>
        </Alert>
      )}

      {/* Testing Options */}
      <Alert>
        <TestTube className="h-4 w-4" />
        <AlertDescription>
          <strong>Testing Options:</strong><br/>
          • <strong>Localhost Testing:</strong> Use "Test Connection" and "Send Test Notification" buttons - works immediately!<br/>
          • <strong>Full Testing:</strong> Install ngrok (<code>npm install -g ngrok</code>), run <code>ngrok http 3001</code>, then use the https URL as webhook<br/>
          • <strong>Production:</strong> When deployed to a real server, webhooks work automatically
        </AlertDescription>
      </Alert>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Bot Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bot className="h-5 w-5" />
              <span>Bot Configuration</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                checked={formData.enabled}
                onCheckedChange={(checked) => handleInputChange('enabled', checked)}
                disabled={!isEditing}
              />
              <Label>Enable Telegram Bot</Label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="botToken">Bot Token</Label>
                <Input
                  id="botToken"
                  type="password"
                  value={formData.botToken}
                  onChange={(e) => handleInputChange('botToken', e.target.value)}
                  disabled={!isEditing}
                  placeholder="123456789:ABCdef1234ghIkl-zyx57W2v1u123ew11"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Get this from @BotFather on Telegram
                </p>
              </div>

              <div>
                <Label htmlFor="adminChatId">Admin Chat ID</Label>
                <Input
                  id="adminChatId"
                  value={formData.adminChatId}
                  onChange={(e) => handleInputChange('adminChatId', e.target.value)}
                  disabled={!isEditing}
                  placeholder="123456789"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Start your bot and send /start to get this
                </p>
              </div>
            </div>

            <div>
              <Label htmlFor="webhookUrl">Webhook URL</Label>
              <div className="space-y-2">
                <div className="flex space-x-2">
                  <Input
                    id="webhookUrl"
                    value={formData.webhookUrl}
                    onChange={(e) => handleInputChange('webhookUrl', e.target.value)}
                    disabled={!isEditing}
                    className="flex-1"
                    placeholder="https://yourdomain.com/api/telegram/webhook"
                  />
                  <Button
                    type="button"
                    onClick={() => setWebhookMutation.mutate({ webhookUrl: formData.webhookUrl })}
                    disabled={!formData.botToken || !formData.webhookUrl || setWebhookMutation.isPending}
                    variant="outline"
                    size="sm"
                  >
                    <Link className="h-4 w-4 mr-2" />
                    {setWebhookMutation.isPending ? 'Setting...' : 'Set Webhook'}
                  </Button>
                  <Button
                    type="button"
                    onClick={() => removeWebhookMutation.mutate()}
                    disabled={!formData.botToken || removeWebhookMutation.isPending}
                    variant="outline"
                    size="sm"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {removeWebhookMutation.isPending ? 'Removing...' : 'Remove'}
                  </Button>
                </div>

                {/* Quick Setup Buttons */}
                <div className="flex flex-wrap gap-2">
                  <Button
                    type="button"
                    onClick={() => handleInputChange('webhookUrl', `${window.location.origin}/api/telegram/webhook`)}
                    disabled={!isEditing}
                    variant="ghost"
                    size="sm"
                    className="text-xs"
                  >
                    Use Current Domain
                  </Button>
                  <Button
                    type="button"
                    onClick={() => handleInputChange('webhookUrl', '')}
                    disabled={!isEditing}
                    variant="ghost"
                    size="sm"
                    className="text-xs"
                  >
                    Clear URL
                  </Button>
                </div>

                <div className="text-xs text-muted-foreground space-y-1">
                  <p><strong>💡 For Testing with ngrok:</strong></p>
                  <p>1. Run: <code className="bg-muted px-1 rounded">ngrok http 3002</code></p>
                  <p>2. Copy the https URL (e.g., https://abc123.ngrok-free.app)</p>
                  <p>3. Enter: <code className="bg-muted px-1 rounded">https://abc123.ngrok-free.app/api/telegram/webhook</code></p>
                  <p>4. Click "Set Webhook" for real-time updates</p>
                  <p><strong>⚠️ Note:</strong> ngrok URLs change each restart - update as needed for testing</p>
                </div>
              </div>
            </div>

            {/* Webhook Status */}
            {webhookInfo && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Webhook Status</Label>
                  <Button
                    type="button"
                    onClick={() => webhookInfoMutation.mutate()}
                    disabled={webhookInfoMutation.isPending}
                    variant="ghost"
                    size="sm"
                  >
                    <RefreshCw className={`h-4 w-4 ${webhookInfoMutation.isPending ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
                <div className="bg-muted p-3 rounded-md text-sm">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div>
                      <strong>URL:</strong> {webhookInfo.url || 'Not set'}
                    </div>
                    <div>
                      <strong>Status:</strong>
                      <Badge variant={webhookInfo.url ? "default" : "secondary"} className="ml-2">
                        {webhookInfo.url ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    {webhookInfo.pending_update_count !== undefined && (
                      <div>
                        <strong>Pending Updates:</strong> {webhookInfo.pending_update_count}
                      </div>
                    )}
                    {webhookInfo.last_error_date && (
                      <div>
                        <strong>Last Error:</strong> {new Date(webhookInfo.last_error_date * 1000).toLocaleString()}
                      </div>
                    )}
                  </div>
                  {webhookInfo.last_error_message && (
                    <div className="mt-2 text-red-600">
                      <strong>Error:</strong> {webhookInfo.last_error_message}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Test Buttons */}
            <div className="flex flex-wrap gap-2">
              <Button
                type="button"
                onClick={() => testConnectionMutation.mutate()}
                disabled={!formData.botToken || testConnectionMutation.isPending}
                variant="outline"
                size="sm"
              >
                <TestTube className="h-4 w-4 mr-2" />
                {testConnectionMutation.isPending ? 'Testing...' : 'Test Connection'}
              </Button>

              <Button
                type="button"
                onClick={() => testNotificationMutation.mutate()}
                disabled={!formData.enabled || !formData.adminChatId || testNotificationMutation.isPending}
                variant="outline"
                size="sm"
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                {testNotificationMutation.isPending ? 'Sending...' : 'Send Test Notification'}
              </Button>

              <Button
                type="button"
                onClick={() => resetCacheMutation.mutate()}
                disabled={!formData.botToken || resetCacheMutation.isPending}
                variant="outline"
                size="sm"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {resetCacheMutation.isPending ? 'Syncing...' : 'Real-time Sync'}
              </Button>
            </div>

            {/* Test Results */}
            {testResults && (
              <Alert>
                {testResults.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription>
                  <strong>{testResults.success ? 'Success:' : 'Error:'}</strong> {testResults.message}
                  {testResults.botInfo && (
                    <div className="mt-2 text-sm">
                      Bot: @{testResults.botInfo.username} ({testResults.botInfo.first_name})
                    </div>
                  )}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Webhook & Real-time Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Link className="h-5 w-5" />
              <span>Webhook & Real-time Management</span>
            </CardTitle>
            <CardDescription>
              Configure webhook for instant updates and real-time synchronization with the web app
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Webhook vs Polling Info */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <strong>Webhook Mode (Recommended):</strong> Instant real-time updates, no caching, always shows current web app state.<br/>
                <strong>Polling Mode:</strong> Checks for updates every second, may have slight delays.
              </AlertDescription>
            </Alert>

            {/* Current Mode Display */}
            <div className="flex items-center justify-between p-3 bg-muted rounded-md">
              <div>
                <div className="font-medium">Current Mode</div>
                <div className="text-sm text-muted-foreground">
                  {webhookInfo?.url ? 'Webhook (Real-time)' : 'Polling (1-second intervals)'}
                </div>
              </div>
              <Badge variant={webhookInfo?.url ? "default" : "secondary"}>
                {webhookInfo?.url ? 'Real-time' : 'Polling'}
              </Badge>
            </div>

            {/* Real-time Sync Controls */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-sm">Real-time Sync</div>
                  <div className="text-xs text-muted-foreground">
                    Clear all cache and sync bot with current web app state
                  </div>
                </div>
                <Button
                  type="button"
                  onClick={() => resetCacheMutation.mutate()}
                  disabled={!formData.botToken || resetCacheMutation.isPending}
                  variant="outline"
                  size="sm"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${resetCacheMutation.isPending ? 'animate-spin' : ''}`} />
                  {resetCacheMutation.isPending ? 'Syncing...' : 'Force Sync Now'}
                </Button>
              </div>

              <Separator />

              <div className="text-xs text-muted-foreground">
                <strong>Real-time Features:</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>No caching - always shows current web app data</li>
                  <li>Instant order notifications via webhook</li>
                  <li>Real-time email template updates</li>
                  <li>Live order status synchronization</li>
                  <li>Immediate configuration changes</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5" />
              <span>Notification Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.notifications.newOrders}
                  onCheckedChange={(checked) => handleNestedChange('notifications', 'newOrders', checked)}
                  disabled={!isEditing}
                />
                <span>New Order Notifications</span>
              </label>

              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.notifications.paymentConfirmations}
                  onCheckedChange={(checked) => handleNestedChange('notifications', 'paymentConfirmations', checked)}
                  disabled={!isEditing}
                />
                <span>Payment Confirmations</span>
              </label>

              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.notifications.trialUpgrades}
                  onCheckedChange={(checked) => handleNestedChange('notifications', 'trialUpgrades', checked)}
                  disabled={!isEditing}
                />
                <span>Trial Upgrades</span>
              </label>

              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.notifications.orderStatusChanges}
                  onCheckedChange={(checked) => handleNestedChange('notifications', 'orderStatusChanges', checked)}
                  disabled={!isEditing}
                />
                <span>Order Status Changes</span>
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Email Integration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5" />
              <span>Email Integration</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.emailIntegration.enabled}
                  onCheckedChange={(checked) => handleNestedChange('emailIntegration', 'enabled', checked)}
                  disabled={!isEditing}
                />
                <span>Enable Email Integration</span>
              </label>

              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.emailIntegration.allowQuickSend}
                  onCheckedChange={(checked) => handleNestedChange('emailIntegration', 'allowQuickSend', checked)}
                  disabled={!isEditing || !formData.emailIntegration.enabled}
                />
                <span>Allow Quick Send from Bot</span>
              </label>
            </div>
          </CardContent>
        </Card>

        {/* M3U Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Link className="h-5 w-5" />
              <span>M3U Management</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.m3uManagement.enabled}
                  onCheckedChange={(checked) => handleNestedChange('m3uManagement', 'enabled', checked)}
                  disabled={!isEditing}
                />
                <span>Enable M3U Management</span>
              </label>

              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.m3uManagement.autoExtractCredentials}
                  onCheckedChange={(checked) => handleNestedChange('m3uManagement', 'autoExtractCredentials', checked)}
                  disabled={!isEditing || !formData.m3uManagement.enabled}
                />
                <span>Auto-Extract Credentials</span>
              </label>

              <div>
                <Label htmlFor="credentialFormat">Credential Format Template</Label>
                <Textarea
                  id="credentialFormat"
                  value={formData.m3uManagement.credentialFormat}
                  onChange={(e) => handleNestedChange('m3uManagement', 'credentialFormat', e.target.value)}
                  disabled={!isEditing || !formData.m3uManagement.enabled}
                  rows={3}
                  placeholder="Username: {username}&#10;Password: {password}&#10;M3U URL: {m3u_url}"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Use {'{username}'}, {'{password}'}, {'{m3u_url}'} as placeholders
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Security Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.security.verifyAdminOnly}
                  onCheckedChange={(checked) => handleNestedChange('security', 'verifyAdminOnly', checked)}
                  disabled={!isEditing}
                />
                <span>Admin Only Access</span>
              </label>

              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.security.rateLimitEnabled}
                  onCheckedChange={(checked) => handleNestedChange('security', 'rateLimitEnabled', checked)}
                  disabled={!isEditing}
                />
                <span>Rate Limiting</span>
              </label>

              <label className="flex items-center space-x-2">
                <Switch
                  checked={formData.security.auditLogging}
                  onCheckedChange={(checked) => handleNestedChange('security', 'auditLogging', checked)}
                  disabled={!isEditing}
                />
                <span>Audit Logging</span>
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Testing Guide */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TestTube className="h-5 w-5" />
              <span>Testing Guide</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-sm mb-2">🚀 Quick Test (Works Now)</h4>
                <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                  <li>Add bot token and Chat ID above</li>
                  <li>Click "Test Connection" to verify bot</li>
                  <li>Click "Send Test Notification" to test messaging</li>
                  <li>Try bot commands: /start, /orders, /status, /help</li>
                </ol>
              </div>

              <div>
                <h4 className="font-medium text-sm mb-2">🔗 Webhook Setup (Recommended for Real-time)</h4>
                <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                  <li>Install: <code className="bg-muted px-1 rounded">npm install -g ngrok</code></li>
                  <li>Run: <code className="bg-muted px-1 rounded">ngrok http 3001</code></li>
                  <li>Copy the https URL (e.g., https://abc123.ngrok.io)</li>
                  <li>Paste: <code className="bg-muted px-1 rounded">https://abc123.ngrok.io/api/telegram/webhook</code></li>
                  <li>Click "Set Webhook" - enables real-time mode with no caching!</li>
                  <li>Use "Force Sync Now" to clear cache and sync with web app</li>
                </ol>
                <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-xs">
                  <strong>✅ Webhook Benefits:</strong> Instant updates, no polling delays, real-time sync with web app
                </div>
              </div>

              <div>
                <h4 className="font-medium text-sm mb-2">📱 Bot Commands to Try</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <code className="bg-muted px-2 py-1 rounded">/start</code>
                  <code className="bg-muted px-2 py-1 rounded">/orders</code>
                  <code className="bg-muted px-2 py-1 rounded">/status</code>
                  <code className="bg-muted px-2 py-1 rounded">/help</code>
                  <code className="bg-muted px-2 py-1 rounded">/test</code>
                  <code className="bg-muted px-2 py-1 rounded">/templates</code>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        {isEditing && (
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              onClick={() => setIsEditing(false)}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateMutation.isPending}
            >
              {updateMutation.isPending ? 'Saving...' : 'Save Settings'}
            </Button>
          </div>
        )}
      </form>
    </div>
  );
};

export default TelegramBotSettings;
