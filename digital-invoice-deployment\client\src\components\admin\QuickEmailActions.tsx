import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Send, RefreshCw } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import EnhancedEmailEditor from '@/components/admin/EnhancedEmailEditor';

interface QuickEmailActionsProps {
  email: string;
  lastSmtpProvider?: string;
}

export default function QuickEmailActions({ email, lastSmtpProvider }: QuickEmailActionsProps) {
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [emailSubject, setEmailSubject] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [isHtml, setIsHtml] = useState(true);
  const [isPreview, setIsPreview] = useState(false);
  const [isSending, setIsSending] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Hardcoded templates for now
  const templates = [
    {
      id: 'affiliate_invitation',
      name: 'Affiliate Invitation',
      subject: 'Join Our Affiliate Program - Earn Commissions!',
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #333; margin-bottom: 10px;">Join Our Affiliate Program</h1>
            <p style="color: #666; font-size: 16px;">Earn commissions by referring new customers</p>
          </div>

          <div style="margin-bottom: 30px;">
            <p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>
            <p style="font-size: 16px; line-height: 1.5;">We're excited to invite you to join our affiliate program! As a valued customer, we believe you'd be a perfect partner to help us grow.</p>
          </div>

          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: center;">
            <h2 style="color: #e63946; font-size: 24px; margin-bottom: 10px;">Earn 20% Commission</h2>
            <p style="font-size: 16px; margin-bottom: 15px;">On every successful referral you make</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Program Benefits:</h3>
            <ul style="padding-left: 20px;">
              <li style="margin-bottom: 5px;">20% commission on all referred sales</li>
              <li style="margin-bottom: 5px;">Monthly payments via PayPal</li>
              <li style="margin-bottom: 5px;">Access to marketing materials</li>
              <li style="margin-bottom: 5px;">Dedicated affiliate support</li>
            </ul>
          </div>
        </div>
      `,
      category: 'marketing'
    },
    {
      id: 'refund_confirmation',
      name: 'Refund Confirmation',
      subject: 'Your Refund Request - Confirmation',
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #333; margin-bottom: 10px;">Refund Confirmation</h1>
          </div>

          <div style="margin-bottom: 30px;">
            <p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>
            <p style="font-size: 16px; line-height: 1.5;">We're writing to confirm that we've processed your refund request.</p>
          </div>

          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <p style="margin: 5px 0;">The refund has been processed and should appear in your account within 5-10 business days, depending on your payment provider.</p>
            <p style="margin: 5px 0;">If you have any questions about this refund, please contact our support team.</p>
          </div>
        </div>
      `,
      category: 'purchase'
    },
    {
      id: 'subscription_expiring',
      name: 'Subscription Expiring Soon',
      subject: 'Your Subscription is Expiring Soon - Action Required',
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #333; margin-bottom: 10px;">Your Subscription is Expiring Soon</h1>
            <p style="color: #666; font-size: 16px;">Action Required to Maintain Access</p>
          </div>

          <div style="margin-bottom: 30px;">
            <p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>
            <p style="font-size: 16px; line-height: 1.5;">This is a friendly reminder that your subscription is set to expire soon. To ensure uninterrupted access to our services, please renew your subscription before the expiration date.</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What Happens If You Don't Renew?</h3>
            <p style="margin: 5px 0;">If your subscription expires, you will lose access to:</p>
            <ul style="padding-left: 20px;">
              <li style="margin-bottom: 5px;">All premium content and features</li>
              <li style="margin-bottom: 5px;">Customer support</li>
              <li style="margin-bottom: 5px;">Future updates and improvements</li>
            </ul>
          </div>
        </div>
      `,
      category: 'notification'
    }
  ];

  const isLoadingTemplates = false;

  // Hardcoded SMTP config for now
  const emailConfig = {
    providers: [
      { id: 'default', name: 'Default SMTP', isDefault: true }
    ]
  };

  // Mutation to send email
  const sendEmailMutation = useMutation({
    mutationFn: (data: any) => {
      console.log('Sending email:', data);
      // For now, just simulate success
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true, message: 'Email sent successfully' });
        }, 1000);
      });
      // In production, this would call the API
      // return apiRequest('/api/send-email', 'POST', data);
    },
    onSuccess: () => {
      setIsEmailDialogOpen(false);
      setEmailSubject('');
      setEmailContent('');
      setSelectedTemplate('');
      setIsSending(false);

      toast({
        title: 'Success',
        description: 'Email sent successfully',
      });

      // Refresh allowed emails list to get updated last subject and SMTP provider
      queryClient.invalidateQueries({ queryKey: ['/api/allowed-emails'] });
    },
    onError: (error: any) => {
      setIsSending(false);
      toast({
        title: 'Error',
        description: error.message || 'Failed to send email',
        variant: 'destructive',
      });
    }
  });

  // Handle template selection
  const handleTemplateSelect = async (templateId: string) => {
    setSelectedTemplate(templateId);

    // Find the template
    const template = templates.find((t: any) => t.id === templateId);

    if (template) {
      // Replace placeholders with customer data
      let subject = template.subject;
      let content = template.htmlContent || template.content;

      // Replace basic placeholders
      const currentDate = new Date();
      const placeholders = {
        customerName: email.split('@')[0], // Use part before @ as name
        customerEmail: email,
        currentYear: currentDate.getFullYear().toString(),
        date: currentDate.toLocaleDateString(),
        orderNumber: `ORD-${Math.floor(Math.random() * 10000)}`, // Random order number for demo
      };

      Object.entries(placeholders).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        subject = subject.replace(regex, value as string);
        content = content.replace(regex, value as string);
      });

      setEmailSubject(subject);
      setEmailContent(content);
      setIsHtml(Boolean(template.htmlContent));

      // Open the email dialog
      setIsEmailDialogOpen(true);
    }
  };

  // Handle sending email
  const handleSendEmail = () => {
    if (!email || !emailSubject || !emailContent) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    setIsSending(true);

    // Find the SMTP provider to use (use the last one used for this email if available)
    let smtpProviderId = lastSmtpProvider;

    // If no last provider, use the default one
    if (!smtpProviderId && emailConfig && emailConfig.providers) {
      const defaultProvider = emailConfig.providers.find((p: any) => p.isDefault);
      if (defaultProvider) {
        smtpProviderId = defaultProvider.id;
      }
    }

    sendEmailMutation.mutate({
      to: email,
      subject: emailSubject,
      content: emailContent,
      smtpProviderId,
      addToAllowedEmails: true,
      isHtml
    });
  };

  // Get template categories
  const getTemplateCategories = () => {
    const categories = new Set<string>();
    templates.forEach((template: any) => {
      if (template.category) {
        categories.add(template.category);
      }
    });
    return Array.from(categories);
  };

  // Group templates by category
  const templatesByCategory = getTemplateCategories().reduce((acc, category) => {
    acc[category] = templates.filter((template: any) => template.category === category);
    return acc;
  }, {} as Record<string, any[]>);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <Send className="h-4 w-4 mr-1" />
            Quick Email
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Send Email</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {isLoadingTemplates ? (
            <DropdownMenuItem disabled>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Loading templates...
            </DropdownMenuItem>
          ) : (
            <>
              {/* Affiliate Email */}
              <DropdownMenuItem onClick={() => handleTemplateSelect('affiliate_invitation')}>
                Affiliate Invitation
              </DropdownMenuItem>

              {/* Refund Email */}
              <DropdownMenuItem onClick={() => handleTemplateSelect('refund_confirmation')}>
                Refund Confirmation
              </DropdownMenuItem>

              {/* Subscription Expiring Email */}
              <DropdownMenuItem onClick={() => handleTemplateSelect('subscription_expiring')}>
                Subscription Expiring
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {/* Custom Email */}
              <DropdownMenuItem onClick={() => {
                setSelectedTemplate('');
                setEmailSubject('');
                setEmailContent('');
                setIsHtml(true);
                setIsEmailDialogOpen(true);
              }}>
                Custom Email...
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Email Dialog */}
      <Dialog open={isEmailDialogOpen} onOpenChange={setIsEmailDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {selectedTemplate ? 'Send Template Email' : 'Send Custom Email'}
            </DialogTitle>
            <DialogDescription>
              Send an email to {email}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email-subject">Subject</Label>
              <Input
                id="email-subject"
                value={emailSubject}
                onChange={(e) => setEmailSubject(e.target.value)}
                placeholder="Enter email subject"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email-content">Content</Label>
              <Tabs defaultValue="edit" value={isPreview ? 'preview' : 'edit'} onValueChange={(value) => setIsPreview(value === 'preview')}>
                <TabsList>
                  <TabsTrigger value="edit">Edit</TabsTrigger>
                  <TabsTrigger value="preview">Preview</TabsTrigger>
                </TabsList>
                <TabsContent value="edit">
                  {isHtml ? (
                    <EnhancedEmailEditor
                      initialHtmlValue={emailContent}
                      onHtmlChange={setEmailContent}
                      height={300}
                      showPreview={false}
                      showSubject={false}
                    />
                  ) : (
                    <Textarea
                      id="email-content"
                      value={emailContent}
                      onChange={(e) => setEmailContent(e.target.value)}
                      placeholder="Enter email content"
                      className="min-h-[300px]"
                    />
                  )}
                </TabsContent>
                <TabsContent value="preview">
                  <div className="border rounded-md p-4 min-h-[300px] bg-white">
                    {isHtml ? (
                      <div dangerouslySetInnerHTML={{ __html: emailContent }} />
                    ) : (
                      <pre className="whitespace-pre-wrap">{emailContent}</pre>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="is-html"
                checked={isHtml}
                onCheckedChange={(checked) => setIsHtml(checked as boolean)}
              />
              <Label htmlFor="is-html">Send as HTML</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEmailDialogOpen(false)} disabled={isSending}>
              Cancel
            </Button>
            <Button onClick={handleSendEmail} disabled={isSending}>
              {isSending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Email
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
