import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight,
  List, ListOrdered, Link, Image, Code, Eye, Save,
  Type, Heading1, Heading2, Heading3
} from 'lucide-react';

interface SimpleRichTextEditorProps {
  initialValue: string;
  onChange: (content: string) => void;
  height?: number;
  placeholder?: string;
  label?: string;
  helpText?: string;
  showPreview?: boolean;
}

const SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({
  initialValue,
  onChange,
  height = 500,
  placeholder = 'Start typing...',
  label,
  helpText,
  showPreview = true,
}) => {
  const [content, setContent] = useState(initialValue);
  const [activeTab, setActiveTab] = useState<string>('visual');
  const editorRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    setContent(initialValue);
  }, [initialValue]);

  const handleCodeChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setContent(newContent);
    onChange(newContent);
  };

  const handleSave = () => {
    onChange(content);
  };

  // Available variables for email templates
  const availableVariables = [
    { name: '{{customerName}}', description: 'Customer\'s full name' },
    { name: '{{customerEmail}}', description: 'Customer\'s email address' },
    { name: '{{productName}}', description: 'Product name' },
    { name: '{{productDescription}}', description: 'Product description' },
    { name: '{{amount}}', description: 'Order amount' },
    { name: '{{orderId}}', description: 'Order ID or reference' },
    { name: '{{orderDate}}', description: 'Order date' },
    { name: '{{paymentLink}}', description: 'Payment link URL' },
    { name: '{{buttonText}}', description: 'Payment button text' },
    { name: '{{businessName}}', description: 'Your business name' },
    { name: '{{currentYear}}', description: 'Current year' },
  ];

  const insertVariable = (variable: string) => {
    if (editorRef.current) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        if (editorRef.current.contains(range.commonAncestorContainer)) {
          const textNode = document.createTextNode(variable);
          range.deleteContents();
          range.insertNode(textNode);

          // Update content state
          setContent(editorRef.current.innerHTML);
          onChange(editorRef.current.innerHTML);

          // Move cursor after inserted variable
          range.setStartAfter(textNode);
          range.setEndAfter(textNode);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      }
    }
  };

  const insertTemplate = (template: string) => {
    if (editorRef.current) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        if (editorRef.current.contains(range.commonAncestorContainer)) {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = template;

          range.deleteContents();

          // Insert all nodes from the template
          const fragment = document.createDocumentFragment();
          while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
          }

          range.insertNode(fragment);

          // Update content state
          setContent(editorRef.current.innerHTML);
          onChange(editorRef.current.innerHTML);
        }
      }
    }
  };

  const execCommand = (command: string, value: string = '') => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      setContent(editorRef.current.innerHTML);
      onChange(editorRef.current.innerHTML);
    }
  };

  return (
    <div className="space-y-2">
      {label && <div className="text-sm font-medium">{label}</div>}
      {helpText && <div className="text-sm text-muted-foreground mb-2">{helpText}</div>}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="visual" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            <span>Visual Editor</span>
          </TabsTrigger>
          <TabsTrigger value="code" className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            <span>HTML Code</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="visual" className="border rounded-md mt-2">
          <div className="border-b p-2 flex flex-wrap gap-1">
            <div className="flex items-center gap-1 mr-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('formatBlock', '<h1>')}
              >
                <Heading1 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('formatBlock', '<h2>')}
              >
                <Heading2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('formatBlock', '<h3>')}
              >
                <Heading3 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('formatBlock', '<p>')}
              >
                <Type className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center gap-1 mr-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('bold')}
              >
                <Bold className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('italic')}
              >
                <Italic className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('underline')}
              >
                <Underline className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center gap-1 mr-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('justifyLeft')}
              >
                <AlignLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('justifyCenter')}
              >
                <AlignCenter className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('justifyRight')}
              >
                <AlignRight className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center gap-1 mr-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('insertUnorderedList')}
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand('insertOrderedList')}
              >
                <ListOrdered className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center gap-1 mr-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => {
                  const url = prompt('Enter URL:');
                  if (url) execCommand('createLink', url);
                }}
              >
                <Link className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => {
                  const url = prompt('Enter image URL:');
                  if (url) execCommand('insertImage', url);
                }}
              >
                <Image className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex-1"></div>

            <select
              className="h-8 px-2 rounded-md border border-input bg-background text-sm"
              onChange={(e) => {
                if (e.target.value) {
                  insertVariable(e.target.value);
                  e.target.value = '';
                }
              }}
            >
              <option value="">Insert Variable</option>
              {availableVariables.map((variable) => (
                <option key={variable.name} value={variable.name}>
                  {variable.name}
                </option>
              ))}
            </select>

            <select
              className="h-8 px-2 rounded-md border border-input bg-background text-sm"
              onChange={(e) => {
                if (e.target.value) {
                  insertTemplate(e.target.value);
                  e.target.value = '';
                }
              }}
            >
              <option value="">Insert Template</option>
              <option value='<div style="text-align: center; margin: 30px 0;"><a href="{{paymentLink}}" style="background-color: #0070BA; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">{{buttonText}}</a></div>'>
                Payment Button
              </option>
              <option value='<div style="border: 1px solid #e9e9e9; padding: 15px; margin: 20px 0; background-color: #f9f9f9;"><table style="width: 100%; border-collapse: collapse;"><tr><td style="padding: 8px; border-bottom: 1px solid #e9e9e9;"><strong>Order ID:</strong></td><td style="padding: 8px; border-bottom: 1px solid #e9e9e9; text-align: right;">{{orderId}}</td></tr><tr><td style="padding: 8px; border-bottom: 1px solid #e9e9e9;"><strong>Date:</strong></td><td style="padding: 8px; border-bottom: 1px solid #e9e9e9; text-align: right;">{{orderDate}}</td></tr><tr><td style="padding: 8px; border-bottom: 1px solid #e9e9e9;"><strong>Item:</strong></td><td style="padding: 8px; border-bottom: 1px solid #e9e9e9; text-align: right;">{{productName}}</td></tr><tr><td style="padding: 8px; border-bottom: 1px solid #e9e9e9;"><strong>Quantity:</strong></td><td style="padding: 8px; border-bottom: 1px solid #e9e9e9; text-align: right;">1</td></tr><tr><td style="padding: 8px;"><strong>Total Amount:</strong></td><td style="padding: 8px; text-align: right; font-weight: bold;">${{amount}}</td></tr></table></div>'>
                Order Summary Table
              </option>
              <option value='<p>Lien: https://example.com/your-link</p>'>
                Link Placeholder
              </option>
            </select>
          </div>

          <div
            ref={editorRef}
            className="p-4 min-h-[300px]"
            style={{ height: `${height - 100}px`, overflowY: 'auto' }}
            contentEditable
            dangerouslySetInnerHTML={{ __html: content }}
            onInput={(e) => {
              const newContent = (e.target as HTMLDivElement).innerHTML;
              setContent(newContent);
              onChange(newContent);
            }}
            onBlur={(e) => {
              const newContent = (e.target as HTMLDivElement).innerHTML;
              setContent(newContent);
              onChange(newContent);
            }}
          />
        </TabsContent>

        <TabsContent value="code" className="border rounded-md mt-2 p-0">
          <textarea
            value={content}
            onChange={handleCodeChange}
            className="w-full p-4 font-mono text-sm resize-none focus:outline-none"
            style={{ height: `${height}px` }}
            placeholder="Enter HTML code here..."
          />
        </TabsContent>
      </Tabs>

      {showPreview && (
        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">Preview</h3>
          <Card>
            <CardContent className="p-4">
              <div
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ __html: content }}
              />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default SimpleRichTextEditor;
