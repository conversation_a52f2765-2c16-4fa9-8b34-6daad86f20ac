import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage-factory';

export const systemMessagesRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  if (req.session && req.session.isAdmin) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Define the validation schema for system messages
const systemMessageSchema = z.object({
  messageId: z.string().min(1, "Message ID is required"),
  category: z.string().min(1, "Category is required"),
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  content: z.string().min(1, "Content is required"),
  isHtml: z.boolean().default(false)
});

// Get all system messages
systemMessagesRouter.get('/', async (req: Request, res: Response) => {
  try {
    const dbMessages = await storage.getSystemMessages();

    // Convert database format to API format
    const messages = dbMessages.map(msg => ({
      id: msg.messageId,
      category: msg.category,
      name: msg.name,
      description: msg.description || '',
      content: msg.content,
      isHtml: Boolean(msg.isHtml)
    }));

    res.json(messages);
  } catch (error) {
    console.error('Error fetching system messages:', error);
    res.status(500).json({ message: 'Failed to fetch system messages' });
  }
});

// Get a specific system message by ID
systemMessagesRouter.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const dbMessage = await storage.getSystemMessageByMessageId(id);

    if (!dbMessage) {
      return res.status(404).json({ message: 'System message not found' });
    }

    // Convert database format to API format
    const message = {
      id: dbMessage.messageId,
      category: dbMessage.category,
      name: dbMessage.name,
      description: dbMessage.description || '',
      content: dbMessage.content,
      isHtml: Boolean(dbMessage.isHtml)
    };

    res.json(message);
  } catch (error) {
    console.error('Error fetching system message:', error);
    res.status(500).json({ message: 'Failed to fetch system message' });
  }
});

// Update a system message
systemMessagesRouter.put('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Validate the request body
    const validatedData = systemMessageSchema.parse(req.body);

    // Check if the message exists
    const existingMessage = await storage.getSystemMessageByMessageId(id);

    if (!existingMessage) {
      return res.status(404).json({ message: 'System message not found' });
    }

    // Convert API format to database format
    const dbUpdate = {
      messageId: validatedData.messageId,
      category: validatedData.category,
      name: validatedData.name,
      description: validatedData.description || '',
      content: validatedData.content,
      isHtml: validatedData.isHtml,
      updatedAt: new Date().toISOString()
    };

    // Update in database
    const updatedDbMessage = await storage.updateSystemMessage(existingMessage.id, dbUpdate);

    if (!updatedDbMessage) {
      return res.status(500).json({ message: 'Failed to update system message' });
    }

    // Convert back to API format
    const responseMessage = {
      id: updatedDbMessage.messageId,
      category: updatedDbMessage.category,
      name: updatedDbMessage.name,
      description: updatedDbMessage.description || '',
      content: updatedDbMessage.content,
      isHtml: Boolean(updatedDbMessage.isHtml)
    };

    res.json(responseMessage);
  } catch (error) {
    console.error('Error updating system message:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update system message' });
  }
});

// Create a new system message
systemMessagesRouter.post('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    // Validate the request body
    const validatedData = systemMessageSchema.parse(req.body);

    // Check if a message with this ID already exists
    const existingMessage = await storage.getSystemMessageByMessageId(validatedData.messageId);

    if (existingMessage) {
      return res.status(409).json({ message: 'System message with this ID already exists' });
    }

    // Convert API format to database format
    const dbMessage = {
      messageId: validatedData.messageId,
      category: validatedData.category,
      name: validatedData.name,
      description: validatedData.description || '',
      content: validatedData.content,
      isHtml: validatedData.isHtml,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Create in database
    const createdDbMessage = await storage.createSystemMessage(dbMessage);

    // Convert back to API format
    const responseMessage = {
      id: createdDbMessage.messageId,
      category: createdDbMessage.category,
      name: createdDbMessage.name,
      description: createdDbMessage.description || '',
      content: createdDbMessage.content,
      isHtml: Boolean(createdDbMessage.isHtml)
    };

    res.status(201).json(responseMessage);
  } catch (error) {
    console.error('Error creating system message:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to create system message' });
  }
});

// Delete a system message
systemMessagesRouter.delete('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Check if the message exists
    const existingMessage = await storage.getSystemMessageByMessageId(id);

    if (!existingMessage) {
      return res.status(404).json({ message: 'System message not found' });
    }

    // Delete from database
    const deleted = await storage.deleteSystemMessage(existingMessage.id);

    if (!deleted) {
      return res.status(500).json({ message: 'Failed to delete system message' });
    }

    res.status(204).send();
  } catch (error) {
    console.error('Error deleting system message:', error);
    res.status(500).json({ message: 'Failed to delete system message' });
  }
});

// Get system message by ID (for internal use)
export const getSystemMessage = async (id: string) => {
  return await storage.getSystemMessageByMessageId(id);
};

// Get all system messages by category (for internal use)
export const getSystemMessagesByCategory = async (category: string) => {
  const allMessages = await storage.getSystemMessages();
  return allMessages.filter(msg => msg.category === category);
};
