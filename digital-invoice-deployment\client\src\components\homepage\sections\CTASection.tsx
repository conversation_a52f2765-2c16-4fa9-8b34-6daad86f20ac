import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { type PageSection, type CTASection as CTASectionType, type ThemeSettings } from '@/api/homepage';

interface CTASectionProps {
  section: PageSection;
  theme?: ThemeSettings;
}

const CTASection: React.FC<CTASectionProps> = ({ section, theme }) => {
  const content = section.content as CTASectionType;

  // Handle button clicks
  const handleButtonClick = (link: string) => {
    if (link) {
      if (link.startsWith('#')) {
        // Smooth scroll to anchor
        const element = document.querySelector(link);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      } else {
        // Navigate to URL
        window.location.href = link;
      }
    }
  };

  // Generate background style
  const getBackgroundStyle = () => {
    switch (content.backgroundType) {
      case 'image':
        return {
          backgroundImage: content.backgroundImage ? `url(${content.backgroundImage})` : undefined,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        };
      case 'gradient':
        return {
          background: content.backgroundColor || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        };
      case 'solid':
        return {
          backgroundColor: content.backgroundColor || '#f8fafc'
        };
      default:
        return {
          backgroundColor: content.backgroundColor || '#f8fafc'
        };
    }
  };

  return (
    <section 
      className="relative py-20 px-4 overflow-hidden"
      style={{
        ...getBackgroundStyle(),
        color: content.textColor || '#1e293b'
      }}
    >
      {/* Background overlay for better text readability */}
      {content.backgroundType === 'image' && (
        <div className="absolute inset-0 bg-black bg-opacity-50"></div>
      )}

      <div className="container mx-auto text-center relative z-10">
        {/* Title */}
        <h2 
          className="text-3xl md:text-5xl font-bold mb-6"
          style={{ color: content.textColor || '#1e293b' }}
        >
          {content.title}
        </h2>

        {/* Description */}
        {content.description && (
          <p 
            className="text-lg md:text-xl mb-10 max-w-3xl mx-auto opacity-90"
            style={{ color: content.textColor || '#1e293b' }}
          >
            {content.description}
          </p>
        )}

        {/* Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          {/* Primary Button */}
          {content.primaryButtonText && (
            <Button
              onClick={() => handleButtonClick(content.primaryButtonLink)}
              size="lg"
              className="text-lg px-8 py-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 min-w-[200px]"
              style={{
                backgroundColor: theme?.primaryColor || '#0070ba',
                borderColor: theme?.primaryColor || '#0070ba',
                color: '#ffffff'
              }}
            >
              {content.primaryButtonText}
            </Button>
          )}

          {/* Secondary Button */}
          {content.secondaryButtonText && (
            <Button
              onClick={() => handleButtonClick(content.secondaryButtonLink)}
              variant="outline"
              size="lg"
              className="text-lg px-8 py-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 min-w-[200px]"
              style={{
                borderColor: content.textColor || theme?.primaryColor || '#0070ba',
                color: content.textColor || theme?.primaryColor || '#0070ba',
                backgroundColor: 'transparent'
              }}
            >
              {content.secondaryButtonText}
            </Button>
          )}
        </div>

        {/* Trust indicators */}
        <div className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-8 opacity-80">
          {/* Security badge */}
          <div className="flex items-center gap-2">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="text-sm font-medium">Secure & Trusted</span>
          </div>

          {/* Money back guarantee */}
          <div className="flex items-center gap-2">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
              <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
            </svg>
            <span className="text-sm font-medium">Money Back Guarantee</span>
          </div>

          {/* 24/7 Support */}
          <div className="flex items-center gap-2">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-2 0c0 .993-.241 1.929-.668 2.754l-1.524-1.525a3.997 3.997 0 00.078-2.183l1.562-1.562C15.802 8.249 16 9.1 16 10zm-5.165 3.913l1.58 1.58A5.98 5.98 0 0110 16a5.976 5.976 0 01-2.516-.552l1.562-1.562a4.006 4.006 0 001.789.027zm-4.677-2.796a4.002 4.002 0 01-.041-2.08l-1.106-1.106A6.003 6.003 0 004 10c0 .639.1 1.255.283 1.836l1.555-1.555zM10 8a2 2 0 100 4 2 2 0 000-4z" clipRule="evenodd" />
            </svg>
            <span className="text-sm font-medium">24/7 Support</span>
          </div>
        </div>
      </div>

      {/* Decorative elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Geometric shapes */}
        <div 
          className="absolute top-10 left-10 w-20 h-20 opacity-10 transform rotate-45"
          style={{ 
            backgroundColor: content.textColor || '#1e293b',
            borderRadius: theme?.borderRadius || '8px'
          }}
        ></div>
        <div 
          className="absolute bottom-10 right-10 w-16 h-16 opacity-10 transform rotate-12"
          style={{ 
            backgroundColor: content.textColor || '#1e293b',
            borderRadius: theme?.borderRadius || '8px'
          }}
        ></div>
        <div 
          className="absolute top-1/2 left-20 w-12 h-12 opacity-10 rounded-full"
          style={{ backgroundColor: content.textColor || '#1e293b' }}
        ></div>
        <div 
          className="absolute top-20 right-1/4 w-8 h-8 opacity-10 rounded-full"
          style={{ backgroundColor: content.textColor || '#1e293b' }}
        ></div>
      </div>

      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-5 pointer-events-none">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, ${content.textColor || '#1e293b'} 2px, transparent 2px), radial-gradient(circle at 75% 75%, ${content.textColor || '#1e293b'} 2px, transparent 2px)`,
          backgroundSize: '50px 50px',
          animation: 'float 20s ease-in-out infinite'
        }}></div>
      </div>

      {/* CSS for animations */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-10px) rotate(1deg); }
        }
      `}</style>
    </section>
  );
};

export default CTASection;
