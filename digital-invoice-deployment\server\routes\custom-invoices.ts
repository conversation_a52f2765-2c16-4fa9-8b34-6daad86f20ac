import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage-factory';
import { insertCustomInvoiceSchema } from '@shared/schema';
import { generateInvoiceNumber } from '../utils/invoice-utils';

export const customInvoicesRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  if (req.session && req.session.isAdmin) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Get all custom invoices (admin only)
customInvoicesRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const invoices = await storage.getCustomInvoices();
    res.json(invoices);
  } catch (error) {
    console.error('Error fetching custom invoices:', error);
    res.status(500).json({ message: 'Failed to fetch custom invoices' });
  }
});

// Get a specific custom invoice by ID (admin only)
customInvoicesRouter.get('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    const invoice = await storage.getCustomInvoice(id);
    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    res.json(invoice);
  } catch (error) {
    console.error('Error fetching custom invoice:', error);
    res.status(500).json({ message: 'Failed to fetch custom invoice' });
  }
});

// Get a specific custom invoice by invoice number (public)
customInvoicesRouter.get('/number/:invoiceNumber', async (req: Request, res: Response) => {
  try {
    const invoiceNumber = req.params.invoiceNumber;
    
    const invoice = await storage.getCustomInvoiceByNumber(invoiceNumber);
    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Increment view count
    await storage.incrementCustomInvoiceViewCount(invoice.id);

    res.json(invoice);
  } catch (error) {
    console.error('Error fetching custom invoice by number:', error);
    res.status(500).json({ message: 'Failed to fetch custom invoice' });
  }
});

// Create a new custom invoice
customInvoicesRouter.post('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    // Generate a unique invoice number
    const invoiceNumber = generateInvoiceNumber();
    
    // Validate the request body
    const invoiceData = {
      ...req.body,
      invoiceNumber,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    const validatedData = insertCustomInvoiceSchema.parse(invoiceData);
    
    // Create the invoice
    const invoice = await storage.createCustomInvoice(validatedData);
    
    res.status(201).json(invoice);
  } catch (error) {
    console.error('Error creating custom invoice:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    
    res.status(500).json({ message: 'Failed to create custom invoice' });
  }
});

// Update a custom invoice
customInvoicesRouter.put('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }
    
    // Check if the invoice exists
    const existingInvoice = await storage.getCustomInvoice(id);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }
    
    // Update the invoice
    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    
    const updatedInvoice = await storage.updateCustomInvoice(id, updateData);
    
    res.json(updatedInvoice);
  } catch (error) {
    console.error('Error updating custom invoice:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    
    res.status(500).json({ message: 'Failed to update custom invoice' });
  }
});

// Mark a custom invoice as paid
customInvoicesRouter.post('/:id/mark-paid', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }
    
    // Check if the invoice exists
    const existingInvoice = await storage.getCustomInvoice(id);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }
    
    // Mark the invoice as paid
    const updatedInvoice = await storage.markCustomInvoiceAsPaid(id);
    
    res.json(updatedInvoice);
  } catch (error) {
    console.error('Error marking custom invoice as paid:', error);
    res.status(500).json({ message: 'Failed to mark invoice as paid' });
  }
});

// Delete a custom invoice
customInvoicesRouter.delete('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }
    
    // Check if the invoice exists
    const existingInvoice = await storage.getCustomInvoice(id);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }
    
    // Delete the invoice
    const success = await storage.deleteCustomInvoice(id);
    
    if (success) {
      res.json({ message: 'Invoice deleted successfully' });
    } else {
      res.status(500).json({ message: 'Failed to delete invoice' });
    }
  } catch (error) {
    console.error('Error deleting custom invoice:', error);
    res.status(500).json({ message: 'Failed to delete custom invoice' });
  }
});
