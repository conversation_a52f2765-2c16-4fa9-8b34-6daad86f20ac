# CloudPanel.io Deployment Checklist

Use this checklist to ensure a smooth deployment of your Digital Invoice application.

## Pre-Deployment Checklist

### Local Preparation
- [ ] Test application locally with `npm run dev`
- [ ] Build production version with `npm run build:prod`
- [ ] Verify all environment variables are set correctly
- [ ] Backup your local database (`data.db`)
- [ ] Test production build locally with `npm run start`

### CloudPanel Server Setup
- [ ] CloudPanel.io server is running and accessible
- [ ] SSH access is configured
- [ ] Domain name is pointed to server IP
- [ ] Node.js 18+ is available on the server

## Deployment Steps

### Step 1: Server Preparation
- [ ] Create new site in CloudPanel dashboard
- [ ] Set application type to "Node.js"
- [ ] Configure domain name
- [ ] Set Node.js version to 18 or higher

### Step 2: File Upload
- [ ] Create project archive (excluding node_modules)
- [ ] Upload files to server via SCP or CloudPanel file manager
- [ ] Extract files in the correct directory
- [ ] Set proper file permissions

### Step 3: Database Configuration

#### For SQLite (Option A):
- [ ] Copy `.env.production` to `.env`
- [ ] Set `DATABASE_URL=sqlite:./data.db`
- [ ] Upload existing database file (if migrating)

#### For MySQL (Option B):
- [ ] Create MySQL database in CloudPanel
- [ ] Set `DATABASE_URL=mysql://user:pass@localhost:3306/dbname`
- [ ] Run database migrations
- [ ] Import existing data (if migrating)

### Step 4: Application Configuration
- [ ] Install dependencies with `npm install --production`
- [ ] Set environment variables in CloudPanel
- [ ] Configure app port to `3001`
- [ ] Set startup file to `dist/index.js`
- [ ] Enable auto-start

### Step 5: Security Setup
- [ ] Generate secure SESSION_SECRET (32+ characters)
- [ ] Change default ADMIN_ACCESS_TOKEN
- [ ] Set SECURE_COOKIES=true
- [ ] Configure SSL certificate (Let's Encrypt)

### Step 6: Launch
- [ ] Start application via CloudPanel
- [ ] Check application logs for errors
- [ ] Test website functionality
- [ ] Verify admin panel access
- [ ] Test payment processing (if applicable)

## Post-Deployment Checklist

### Monitoring Setup
- [ ] Configure application monitoring
- [ ] Set up log rotation
- [ ] Install PM2 for process management (optional)
- [ ] Create health check endpoints

### Backup Configuration
- [ ] Set up automated database backups
- [ ] Configure file backup for uploads
- [ ] Test backup restoration process
- [ ] Schedule regular backup cleanup

### Performance Optimization
- [ ] Enable Nginx caching (if needed)
- [ ] Configure database optimization
- [ ] Set up CDN (if needed)
- [ ] Monitor resource usage

### Security Hardening
- [ ] Configure firewall rules
- [ ] Add security headers to Nginx
- [ ] Set up fail2ban (optional)
- [ ] Regular security updates schedule

## Testing Checklist

### Functionality Tests
- [ ] Homepage loads correctly
- [ ] User registration/login works
- [ ] Product creation and management
- [ ] Invoice generation and sending
- [ ] Payment processing (if configured)
- [ ] Email functionality (if configured)
- [ ] File uploads work correctly

### Performance Tests
- [ ] Page load times are acceptable
- [ ] Database queries are optimized
- [ ] Memory usage is within limits
- [ ] CPU usage is reasonable

### Security Tests
- [ ] HTTPS is working correctly
- [ ] Admin panel is secure
- [ ] File upload restrictions work
- [ ] Session management is secure

## Troubleshooting Common Issues

### Application Won't Start
- [ ] Check Node.js version compatibility
- [ ] Verify all environment variables
- [ ] Check file permissions
- [ ] Review application logs

### Database Connection Issues
- [ ] Verify DATABASE_URL format
- [ ] Check database credentials
- [ ] Ensure database exists
- [ ] Test database connectivity

### Performance Issues
- [ ] Check server resources (CPU, RAM)
- [ ] Monitor database performance
- [ ] Review application logs for errors
- [ ] Optimize database queries

## Environment Variables Template

Create your `.env.production` file with these variables:

```env
# Required Variables
DATABASE_URL=sqlite:./data.db
SESSION_SECRET=your-secure-session-secret-minimum-32-characters
NODE_ENV=production
SECURE_COOKIES=true
ADMIN_ACCESS_TOKEN=your-secure-admin-token

# Optional Variables
USE_DATABASE=true
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Your App Name
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_ENVIRONMENT=production
```

## Quick Commands Reference

### Build and Deploy
```bash
# Local build
npm run build:prod

# Upload to server
scp -r dist/ root@server-ip:/home/<USER>/htdocs/

# Install dependencies on server
npm install --production

# Start application
npm run start
```

### Database Operations
```bash
# SQLite backup
cp data.db backups/data-$(date +%Y%m%d).db

# MySQL backup
mysqldump -u user -p database > backup.sql

# Database migration
npm run db:push
```

### Process Management
```bash
# With PM2
pm2 start dist/index.js --name digital-invoice
pm2 startup
pm2 save

# Check status
pm2 status
pm2 logs digital-invoice
```

## Support Resources

- **CloudPanel Documentation**: https://www.cloudpanel.io/docs/
- **Node.js Documentation**: https://nodejs.org/docs/
- **Application Logs**: Check CloudPanel dashboard → Your Site → Logs

## Final Verification

After completing all steps:
- [ ] Application is accessible via your domain
- [ ] SSL certificate is active and working
- [ ] All features are functioning correctly
- [ ] Monitoring and backups are configured
- [ ] Performance is acceptable
- [ ] Security measures are in place

**Congratulations! Your Digital Invoice application is now successfully deployed on CloudPanel.io!**

## Next Steps

1. **Monitor Performance**: Keep an eye on server resources and application performance
2. **Regular Updates**: Plan for regular application and security updates
3. **Backup Testing**: Regularly test your backup and restoration procedures
4. **User Training**: Train your team on using the deployed application
5. **Documentation**: Keep deployment documentation updated for future reference
