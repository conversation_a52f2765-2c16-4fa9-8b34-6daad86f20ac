@echo off
REM CloudPanel.io Startup Script for Digital Invoice Application (Windows)

echo 🚀 Starting Digital Invoice Application...

REM Check if .env exists
if not exist ".env" (
    echo ⚠️  .env file not found, copying from .env.production
    copy .env.production .env
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install --production
)

REM Start the application
echo ▶️  Starting application...
npm start
