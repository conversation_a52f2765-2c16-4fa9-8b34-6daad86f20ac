import express from 'express';
import { systemMonitor } from '../services/system-monitor';
import { telegramBot } from '../services/telegram-bot';

const router = express.Router();

// Middleware to check admin authentication
const requireAdmin = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (!req.session?.isAdmin) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  next();
};

/**
 * Get current monitoring status
 */
router.get('/status', requireAdmin, async (req, res) => {
  try {
    const status = {
      isActive: systemMonitor.isActive,
      currentMetrics: systemMonitor.currentMetrics,
      recentErrors: systemMonitor.recentErrors.length,
    };

    res.json(status);
  } catch (error) {
    console.error('Error getting monitoring status:', error);
    res.status(500).json({ 
      message: 'Failed to get monitoring status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get current system metrics
 */
router.get('/metrics', requireAdmin, async (req, res) => {
  try {
    const metrics = systemMonitor.currentMetrics;
    
    if (!metrics) {
      return res.status(404).json({ message: 'No metrics available' });
    }

    res.json(metrics);
  } catch (error) {
    console.error('Error getting system metrics:', error);
    res.status(500).json({ 
      message: 'Failed to get system metrics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get monitoring configuration
 */
router.get('/config', requireAdmin, async (req, res) => {
  try {
    // Return default configuration (in a real app, this would be stored in database)
    const config = {
      enabled: systemMonitor.isActive,
      intervalMinutes: 5,
      alertThresholds: {
        cpu: 80,
        memory: 85,
        disk: 90,
        responseTime: 5000,
      },
    };

    res.json(config);
  } catch (error) {
    console.error('Error getting monitoring config:', error);
    res.status(500).json({ 
      message: 'Failed to get monitoring configuration',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Update monitoring configuration
 */
router.put('/config', requireAdmin, async (req, res) => {
  try {
    const { alertThresholds, intervalMinutes } = req.body;

    // Update alert thresholds
    if (alertThresholds) {
      systemMonitor.updateThresholds(alertThresholds);
    }

    // Restart monitoring with new interval if it's active
    if (systemMonitor.isActive && intervalMinutes) {
      systemMonitor.stopMonitoring();
      await systemMonitor.startMonitoring(intervalMinutes);
    }

    res.json({ 
      message: 'Configuration updated successfully',
      config: {
        enabled: systemMonitor.isActive,
        intervalMinutes: intervalMinutes || 5,
        alertThresholds: alertThresholds || {
          cpu: 80,
          memory: 85,
          disk: 90,
          responseTime: 5000,
        },
      }
    });
  } catch (error) {
    console.error('Error updating monitoring config:', error);
    res.status(500).json({ 
      message: 'Failed to update monitoring configuration',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Toggle system monitoring on/off
 */
router.post('/toggle', requireAdmin, async (req, res) => {
  try {
    const { enabled } = req.body;

    if (enabled && !systemMonitor.isActive) {
      await systemMonitor.startMonitoring(5); // Default 5 minute intervals
      res.json({ 
        message: 'System monitoring started',
        isActive: true
      });
    } else if (!enabled && systemMonitor.isActive) {
      systemMonitor.stopMonitoring();
      res.json({ 
        message: 'System monitoring stopped',
        isActive: false
      });
    } else {
      res.json({ 
        message: `System monitoring is already ${enabled ? 'active' : 'inactive'}`,
        isActive: systemMonitor.isActive
      });
    }
  } catch (error) {
    console.error('Error toggling monitoring:', error);
    res.status(500).json({ 
      message: 'Failed to toggle monitoring',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get recent error logs
 */
router.get('/errors', requireAdmin, async (req, res) => {
  try {
    const errors = systemMonitor.recentErrors;
    res.json(errors);
  } catch (error) {
    console.error('Error getting error logs:', error);
    res.status(500).json({ 
      message: 'Failed to get error logs',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Send test alert to Telegram
 */
router.post('/test-alert', requireAdmin, async (req, res) => {
  try {
    const testMessage = `🧪 **System Monitoring Test Alert**

This is a test alert to verify that system monitoring notifications are working correctly.

📊 **Test Metrics:**
🖥️ **CPU:** Test - 45.2%
💾 **Memory:** Test - 62.8%
💿 **Disk:** Test - 34.1%
⏱️ **Uptime:** Test - 2d 14h 32m

✅ If you received this message, system monitoring alerts are working properly!

⏰ **Time:** ${new Date().toLocaleString()}`;

    const success = await telegramBot.sendSystemMessage(testMessage);
    
    if (success) {
      res.json({ message: 'Test alert sent successfully' });
    } else {
      res.status(500).json({ message: 'Failed to send test alert. Check Telegram bot configuration.' });
    }
  } catch (error) {
    console.error('Error sending test alert:', error);
    res.status(500).json({ 
      message: 'Failed to send test alert',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get system performance report
 */
router.get('/report', requireAdmin, async (req, res) => {
  try {
    const report = await systemMonitor.getSystemReport();
    res.json({ report });
  } catch (error) {
    console.error('Error getting system report:', error);
    res.status(500).json({ 
      message: 'Failed to get system report',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Manually trigger error log (for testing)
 */
router.post('/test-error', requireAdmin, async (req, res) => {
  try {
    const { level = 'error', message = 'Test error message' } = req.body;
    
    await systemMonitor.logError(
      level as 'error' | 'warning' | 'critical',
      `Manual test error: ${message}`,
      'Test stack trace for demonstration purposes',
      'admin-test'
    );

    res.json({ message: 'Test error logged successfully' });
  } catch (error) {
    console.error('Error logging test error:', error);
    res.status(500).json({ 
      message: 'Failed to log test error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
