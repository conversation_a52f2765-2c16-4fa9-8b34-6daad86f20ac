import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Trash2, Edit, Plus, Code, ExternalLink } from 'lucide-react';

// Schema for embed code form
const embedCodeSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  platform: z.string().min(1, 'Platform is required'),
  headScript: z.string().min(1, 'Head script is required'),
  buttonHtml: z.string().min(1, 'Button HTML is required'),
  active: z.boolean().default(true),
});

type EmbedCodeFormValues = z.infer<typeof embedCodeSchema>;

interface EmbedCode {
  id: string;
  name: string;
  description: string;
  platform: string;
  headScript: string;
  buttonHtml: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface EmbedCodeTemplate {
  name: string;
  platform: string;
  headScript: string;
  buttonHtml: string;
  description: string;
}

const EmbedCodeSettings: React.FC = () => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingEmbedCode, setEditingEmbedCode] = useState<EmbedCode | null>(null);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Fetch embed codes
  const { data: embedCodes = [], isLoading } = useQuery<EmbedCode[]>({
    queryKey: ['embed-codes'],
    queryFn: async () => {
      const response = await fetch('/api/embed-codes');
      if (!response.ok) throw new Error('Failed to fetch embed codes');
      return response.json();
    },
  });

  // Fetch embed code templates
  const { data: templates = {} } = useQuery<Record<string, EmbedCodeTemplate>>({
    queryKey: ['embed-code-templates'],
    queryFn: async () => {
      const response = await fetch('/api/embed-codes/templates');
      if (!response.ok) throw new Error('Failed to fetch templates');
      return response.json();
    },
  });

  // Form for adding/editing embed codes
  const form = useForm<EmbedCodeFormValues>({
    resolver: zodResolver(embedCodeSchema),
    defaultValues: {
      name: '',
      description: '',
      platform: 'custom',
      headScript: '',
      buttonHtml: '',
      active: true,
    },
  });

  // Create embed code mutation
  const createMutation = useMutation({
    mutationFn: async (data: EmbedCodeFormValues) => {
      const response = await fetch('/api/embed-codes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error('Failed to create embed code');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['embed-codes'] });
      setIsAddDialogOpen(false);
      form.reset();
      toast({
        title: 'Success',
        description: 'Embed code created successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to create embed code: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Update embed code mutation
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: EmbedCodeFormValues }) => {
      const response = await fetch(`/api/embed-codes/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error('Failed to update embed code');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['embed-codes'] });
      setIsEditDialogOpen(false);
      setEditingEmbedCode(null);
      form.reset();
      toast({
        title: 'Success',
        description: 'Embed code updated successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update embed code: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Delete embed code mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/embed-codes/${id}`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to delete embed code');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['embed-codes'] });
      toast({
        title: 'Success',
        description: 'Embed code deleted successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to delete embed code: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (data: EmbedCodeFormValues) => {
    if (editingEmbedCode) {
      updateMutation.mutate({ id: editingEmbedCode.id, data });
    } else {
      createMutation.mutate(data);
    }
  };

  const handleEdit = (embedCode: EmbedCode) => {
    setEditingEmbedCode(embedCode);
    form.reset({
      name: embedCode.name,
      description: embedCode.description,
      platform: embedCode.platform,
      headScript: embedCode.headScript,
      buttonHtml: embedCode.buttonHtml,
      active: embedCode.active,
    });
    setIsEditDialogOpen(true);
  };

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this embed code?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleTemplateSelect = (templateKey: string) => {
    const template = templates[templateKey];
    if (template) {
      form.setValue('name', template.name);
      form.setValue('platform', template.platform);
      form.setValue('headScript', template.headScript);
      form.setValue('buttonHtml', template.buttonHtml);
      form.setValue('description', template.description);
    }
  };

  const openAddDialog = () => {
    setEditingEmbedCode(null);
    form.reset({
      name: '',
      description: '',
      platform: 'custom',
      headScript: '',
      buttonHtml: '',
      active: true,
    });
    setIsAddDialogOpen(true);
  };

  if (isLoading) {
    return <div>Loading embed codes...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Embed Code Settings</h2>
          <p className="text-muted-foreground">
            Manage external payment platform embed codes like BillGang.io, SellPass, etc.
          </p>
        </div>
        <Button onClick={openAddDialog}>
          <Plus className="h-4 w-4 mr-2" />
          Add Embed Code
        </Button>
      </div>

      {/* Embed Codes List */}
      <div className="grid gap-4">
        {embedCodes.map((embedCode) => (
          <Card key={embedCode.id}>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-4 w-4" />
                    {embedCode.name}
                    <Badge variant={embedCode.active ? 'default' : 'secondary'}>
                      {embedCode.active ? 'Active' : 'Inactive'}
                    </Badge>
                  </CardTitle>
                  <CardDescription>{embedCode.description}</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(embedCode)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(embedCode.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <span className="text-sm font-medium">Platform:</span>
                  <span className="ml-2 text-sm text-muted-foreground">{embedCode.platform}</span>
                </div>
                <div>
                  <span className="text-sm font-medium">Head Script:</span>
                  <pre className="mt-1 p-2 bg-muted rounded text-xs overflow-x-auto">
                    {embedCode.headScript}
                  </pre>
                </div>
                <div>
                  <span className="text-sm font-medium">Button HTML:</span>
                  <pre className="mt-1 p-2 bg-muted rounded text-xs overflow-x-auto">
                    {embedCode.buttonHtml}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add/Edit Dialog */}
      <Dialog open={isAddDialogOpen || isEditDialogOpen} onOpenChange={(open) => {
        if (!open) {
          setIsAddDialogOpen(false);
          setIsEditDialogOpen(false);
          setEditingEmbedCode(null);
        }
      }}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingEmbedCode ? 'Edit Embed Code' : 'Add New Embed Code'}
            </DialogTitle>
            <DialogDescription>
              Configure an external payment platform embed code with head script and button HTML.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              {/* Template Selection */}
              {!editingEmbedCode && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Quick Templates</label>
                  <div className="flex gap-2">
                    {Object.entries(templates).map(([key, template]) => (
                      <Button
                        key={key}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleTemplateSelect(key)}
                      >
                        {template.name}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., BillGang Payment" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Input placeholder="Optional description" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="platform"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Platform</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., billgang, sellpass, custom" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="headScript"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Head Script</FormLabel>
                    <FormDescription>
                      Script to be added to the &lt;head&gt; section of the checkout page
                    </FormDescription>
                    <FormControl>
                      <Textarea
                        placeholder='<script src="https://platform.example.com/embed.js"></script>'
                        className="font-mono text-sm"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="buttonHtml"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Button HTML</FormLabel>
                    <FormDescription>
                      HTML code for the payment button/embed
                    </FormDescription>
                    <FormControl>
                      <Textarea
                        placeholder='<button data-product-id="your-id">Purchase</button>'
                        className="font-mono text-sm"
                        rows={5}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>Active</FormLabel>
                      <FormDescription>
                        Enable this embed code for use in checkout pages
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsAddDialogOpen(false);
                    setIsEditDialogOpen(false);
                    setEditingEmbedCode(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createMutation.isPending || updateMutation.isPending}
                >
                  {editingEmbedCode ? 'Update' : 'Create'} Embed Code
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EmbedCodeSettings;
