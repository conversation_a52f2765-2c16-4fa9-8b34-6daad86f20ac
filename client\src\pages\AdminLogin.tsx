import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useLocation, Link } from 'wouter';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { useAdminAuth } from '@/hooks/use-admin-auth';
import { Loader2 } from 'lucide-react';
import TwoFactorAuth from '@/components/admin/TwoFactorAuth';

// Validation schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().default(false),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function AdminLogin() {
  const [, setLocation] = useLocation();
  const { login, isAuthenticated, isLoading, isPending, authState } = useAdminAuth();
  const [showTwoFactor, setShowTwoFactor] = useState(false);

  // Form setup
  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false,
    },
  });

  // Check for access token in URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const accessToken = urlParams.get('access');

    if (accessToken && !isAuthenticated) {
      // Automatically login with access token
      login({ accessToken, rememberMe: false });

      // Clean the URL to hide the token
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [login, isAuthenticated]);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      // Small delay to ensure proper state synchronization
      setTimeout(() => {
        setLocation('/admin/dashboard');
      }, 50);
    }

    // Show 2FA form if required
    if (authState?.requiresTwoFactor) {
      setShowTwoFactor(true);
    }
  }, [isAuthenticated, isLoading, authState, setLocation]);

  // Submit handler
  const onSubmit = (data: LoginFormData) => {
    login(data);
  };

  // Handle 2FA success
  const handleTwoFactorSuccess = () => {
    console.log("2FA verification successful, redirecting to dashboard...");

    try {
      // Try to refresh auth state, but don't block if it fails
      if (checkAuth) {
        checkAuth();
      }
    } catch (e) {
      console.error("Error refreshing auth state:", e);
    }

    // The redirect is now handled directly in the TwoFactorAuth component
    // This is just a backup in case the direct redirect fails
    setTimeout(() => {
      window.location.href = '/admin/dashboard';
    }, 1500);
  };

  // Handle 2FA cancel
  const handleTwoFactorCancel = () => {
    setShowTwoFactor(false);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-50">
      {showTwoFactor ? (
        <TwoFactorAuth
          onSuccess={handleTwoFactorSuccess}
          onCancel={handleTwoFactorCancel}
        />
      ) : (
        <Card className="w-full max-w-md shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="text-2xl text-center">Admin Login</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input placeholder="admin" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="••••••••" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rememberMe"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Remember me
                        </FormLabel>
                        <FormDescription>
                          Stay logged in for 30 days
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isPending}
                >
                  {isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Logging in...
                    </>
                  ) : "Log In"}
                </Button>
              </form>
            </Form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Link href="/admin/forgot-password" className="text-sm text-blue-600 hover:text-blue-800">
              Forgot Password?
            </Link>
          </CardFooter>
        </Card>
      )}
    </div>
  );
}