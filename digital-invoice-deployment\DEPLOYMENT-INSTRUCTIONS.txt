# CloudPanel.io Deployment Instructions

## Quick Start

1. **Upload Files**
   - Upload this entire package to your CloudPanel.io server
   - Extract to your site's htdocs directory

2. **Install Dependencies**
   ```bash
   npm install --production
   ```

3. **Configure Environment**
   - Copy .env.production to .env
   - Update database credentials if using MySQL
   - Update SESSION_SECRET and ADMIN_ACCESS_TOKEN

4. **Set CloudPanel Configuration**
   - App Type: Node.js
   - App Port: 3001
   - Startup File: dist/index.js
   - Node.js Version: 18+

5. **Start Application**
   ```bash
   npm start
   ```

## Environment Variables

Required variables in .env:
- DATABASE_URL=sqlite:./data.db (or MySQL URL)
- SESSION_SECRET=your-secure-session-secret
- NODE_ENV=production
- SECURE_COOKIES=true
- ADMIN_ACCESS_TOKEN=your-admin-token

## Database Options

### SQLite (Default)
- No additional setup required
- Database file: data.db

### MySQL (Optional)
- Create MySQL database in CloudPanel
- Update DATABASE_URL in .env
- Run: npm run db:push

## Troubleshooting

- Check CloudPanel logs for errors
- Verify Node.js version (18+)
- Ensure all environment variables are set
- Check file permissions

For detailed instructions, see README-CLOUDPANEL-DEPLOYMENT.md
