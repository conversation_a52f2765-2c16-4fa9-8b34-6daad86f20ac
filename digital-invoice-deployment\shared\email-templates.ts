/**
 * Email Templates Configuration
 * This file defines the structure and default values for email templates
 * that can be customized through the admin interface.
 */

export interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  category: string;
  isDefault: boolean;
}

export interface EmailTemplateCategory {
  id: string;
  name: string;
  description: string;
}

export const EMAIL_TEMPLATE_CATEGORIES: EmailTemplateCategory[] = [
  {
    id: 'purchase',
    name: 'Purchase Emails',
    description: 'Emails sent when a customer makes a purchase'
  },
  {
    id: 'trial',
    name: 'Trial Emails',
    description: 'Emails sent for trial subscriptions'
  },
  {
    id: 'iptv',
    name: 'IPTV & Streaming',
    description: 'Emails for IPTV subscriptions and streaming services'
  },
  {
    id: 'notification',
    name: 'Notification Emails',
    description: 'General notification emails'
  },
  {
    id: 'marketing',
    name: 'Marketing Emails',
    description: 'Emails for marketing campaigns'
  },
  {
    id: 'support',
    name: 'Support Emails',
    description: 'Emails for customer support'
  }
];

// Default email templates
export const DEFAULT_EMAIL_TEMPLATES: EmailTemplate[] = [
  // Purchase Emails
  {
    id: 'purchase_confirmation',
    name: 'Purchase Confirmation',
    description: 'Sent to customers after a successful purchase',
    subject: 'Your Purchase Confirmation - {{orderNumber}}',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #333;">Thank You for Your Purchase!</h1>
          <p style="color: #666;">Order #` + `{{orderNumber}}` + `</p>
        </div>

        <div style="margin-bottom: 30px;">
          <h3 style="color: #333;">Order Details:</h3>
          <p><strong>Invoice Number:</strong> ` + `{{orderNumber}}` + `</p>
          <p><strong>Date:</strong> ` + `{{orderDate}}` + `</p>
          <p><strong>Product:</strong> ` + `{{productName}}` + `</p>
          <p><strong>Amount:</strong> $` + `{{amount}}` + `</p>
          <p><strong>Customer:</strong> ` + `{{customerName}}` + `</p>
        </div>

        <div style="margin-bottom: 30px;">
          <h3 style="color: #333;">What's Next?</h3>
          <p>Your purchase has been processed successfully. You will receive your product details within 8 hours during working hours.</p>
          <p>If you have any questions, please contact our support team.</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
          <p>If you have any questions, please reply to this email.</p>
          <p><strong>Thank you,<br>Support Team</strong></p>
        </div>
      </div>
    `,
    textContent: 'Thank you for your purchase! Order #{{orderNumber}}. Product: {{productName}}. Price: {{price}}. Date: {{date}}. Customer: {{customerName}} ({{customerEmail}}). Your purchase has been processed successfully. You will receive your product details shortly. If you have any questions, please contact our support team.',
    category: 'purchase',
    isDefault: true
  },

  // Trial Emails
  {
    id: 'trial_confirmation',
    name: 'Trial Subscription Confirmation',
    description: 'Sent to customers after starting a trial subscription',
    subject: 'Your Trial Subscription is Active!',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #333; margin-bottom: 10px;">Your Trial is Active!</h1>
          <p style="color: #666; font-size: 16px;">Order #{{orderNumber}}</p>
        </div>

        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="color: #333; margin-top: 0;">Trial Details:</h3>
          <p><strong>Invoice Number:</strong> ` + `{{orderNumber}}` + `</p>
          <p><strong>Date:</strong> ` + `{{date}}` + `</p>
          <p><strong>Product:</strong> ` + `{{productName}}` + `</p>
          <p><strong>Trial Period:</strong> 24 hours</p>
          <p><strong>Customer:</strong> ` + `{{customerName}}` + `</p>
        </div>

        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <p style="margin: 0; color: #856404;"><strong>Note:</strong> After order confirmation, you will receive your subscription by email within 8 hours (during working hours), but generally all subscriptions are sent within 3 hours of your order. Please check your spam folder if you cannot find the email.</p>
        </div>

        <div style="margin-bottom: 30px;">
          <h2 style="color: #333; font-size: 18px; margin-bottom: 10px;">Access Information</h2>
          <p style="margin: 5px 0;">You can access your trial subscription using the following details:</p>
          <p style="margin: 5px 0;"><strong>Username:</strong> {{customerEmail}}</p>
          <p style="margin: 5px 0;"><strong>App Type:</strong> {{appType}}</p>
          {{#if macAddress}}
          <p style="margin: 5px 0;"><strong>MAC Address:</strong> {{macAddress}}</p>
          {{/if}}
        </div>

        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <h2 style="color: #333; font-size: 18px; margin-bottom: 10px;">Upgrade to Full Access</h2>
          <p style="margin: 5px 0;">Enjoying your trial? Upgrade to a full subscription for unlimited access to all features.</p>
          <div style="text-align: center; margin-top: 15px;">
            <a href="{{upgradeLink}}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">Upgrade Now</a>
          </div>
        </div>

        <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
          <p style="margin: 0; color: #495057;">If you have any questions, please reply to this email.</p>
          <p style="margin: 5px 0 0 0; color: #495057;"><strong>Thank you,<br>Support Team</strong></p>
        </div>
      </div>
    `,
    textContent: 'Your trial is active! Order #{{orderNumber}}. Product: {{productName}}. Trial Period: 24 hours. Start Date: {{date}}. Access Information - Username: {{customerEmail}}, App Type: {{appType}}. Enjoying your trial? Upgrade to a full subscription for unlimited access to all features.',
    category: 'trial',
    isDefault: true
  },

  // Notification Emails
  {
    id: 'order_shipped',
    name: 'Order Shipped',
    description: 'Sent to customers when their order has been shipped',
    subject: 'Your Order Has Been Shipped - {{orderNumber}}',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #333; margin-bottom: 10px;">Your Order Has Been Shipped!</h1>
          <p style="color: #666; font-size: 16px;">Order #{{orderNumber}}</p>
        </div>

        <div style="margin-bottom: 30px;">
          <p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>
          <p style="font-size: 16px; line-height: 1.5;">We're happy to inform you that your order has been shipped and is on its way to you!</p>
        </div>

        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="color: #333; margin-top: 0;">Order Details:</h3>
          <p><strong>Invoice Number:</strong> ` + `{{orderNumber}}` + `</p>
          <p><strong>Date:</strong> ` + `{{orderDate}}` + `</p>
          <p><strong>Product:</strong> ` + `{{productName}}` + `</p>
          <p><strong>Shipping Date:</strong> ` + `{{shippingDate}}` + `</p>
          <p><strong>Customer:</strong> ` + `{{customerName}}` + `</p>
        </div>

        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <p style="margin: 0; color: #856404;"><strong>Note:</strong> After order confirmation, you will receive your subscription by email within 8 hours (during working hours), but generally all subscriptions are sent within 3 hours of your order. Please check your spam folder if you cannot find the email.</p>
        </div>

        <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
          <p style="margin: 0; color: #495057;">If you have any questions, please reply to this email.</p>
          <p style="margin: 5px 0 0 0; color: #495057;"><strong>Thank you,<br>Support Team</strong></p>
        </div>
      </div>
    `,
    textContent: 'Your order has been shipped! Order #{{orderNumber}}. Dear {{customerName}}, We are happy to inform you that your order has been shipped and is on its way to you! Product: {{productName}}. Order Date: {{orderDate}}. Shipping Date: {{shippingDate}}.',
    category: 'notification',
    isDefault: true
  },

  // Marketing Emails
  {
    id: 'special_offer',
    name: 'Special Offer',
    description: 'Promotional email for special offers',
    subject: 'Special Offer Just for You!',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #333; margin-bottom: 10px;">Special Offer Just for You!</h1>
          <p style="color: #666; font-size: 16px;">Limited Time Only</p>
        </div>

        <div style="margin-bottom: 30px;">
          <p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>
          <p style="font-size: 16px; line-height: 1.5;">We're excited to offer you a special discount on our premium services!</p>
        </div>

        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: center;">
          <h2 style="color: #e63946; font-size: 24px; margin-bottom: 10px;">Save 20% Today!</h2>
          <p style="font-size: 16px; margin-bottom: 15px;">Use code: <strong>SPECIAL20</strong> at checkout</p>
          <div style="margin-top: 15px;">
            <a href="{{offerLink}}" style="display: inline-block; padding: 10px 20px; background-color: #e63946; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">Shop Now</a>
          </div>
        </div>

        <div style="margin-bottom: 30px;">
          <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Offer Details:</h3>
          <ul style="padding-left: 20px;">
            <li style="margin-bottom: 5px;">20% off all premium subscriptions</li>
            <li style="margin-bottom: 5px;">Valid until {{expiryDate}}</li>
            <li style="margin-bottom: 5px;">Cannot be combined with other offers</li>
          </ul>
        </div>

        <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
          <p style="margin: 0; color: #495057;">If you have any questions, please reply to this email.</p>
          <p style="margin: 5px 0 0 0; color: #495057;"><strong>Thank you,<br>Support Team</strong></p>
        </div>
      </div>
    `,
    textContent: 'Special Offer Just for You! Limited Time Only. Dear {{customerName}}, We are excited to offer you a special discount on our premium services! Save 20% Today! Use code: SPECIAL20 at checkout. Offer Details: 20% off all premium subscriptions, Valid until {{expiryDate}}, Cannot be combined with other offers.',
    category: 'marketing',
    isDefault: true
  },

  // IPTV Subscription Email
  {
    id: 'smartonn_template',
    name: 'Smartonn',
    description: 'IPTV subscription email with credentials and installation links',
    subject: 'Your IPTV Subscription Details - {{customerName}}',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #333;">Your IPTV Subscription is Ready!</h1>
          <p style="color: #666;">Order #{{orderId}}</p>
        </div>

        <div style="margin-bottom: 30px;">
          <p>Dear {{customerName}},</p>
          <p>Here is your subscription as an M3U link:</p>
          <p><strong>{{m3uLink}}</strong></p>
        </div>

        <div style="margin-bottom: 30px;">
          <h3 style="color: #333;">Login Details:</h3>
          <p><strong>Username:</strong> {{username}}</p>
          <p><strong>Password:</strong> {{password}}</p>
          <p><strong>Server 1:</strong> http://premium.pro4ott.com:8789</p>
          <p><strong>Server 2:</strong> http://live.mypythontv.com:2052</p>
          <p><strong>Server 3:</strong> http://mypythonpremium.com:2052</p>
        </div>

        <div style="margin-bottom: 30px;">
          <h3 style="color: #333;">Installation Guide:</h3>
          <p>Visit: https://smartonn.com/install/</p>
        </div>

        <div style="margin-bottom: 30px;">
          <h3 style="color: #333;">Support:</h3>
          <p>If you have any problems, contact us at:</p>
          <p><strong>Email:</strong> <EMAIL></p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
          <p>Thank you for choosing our IPTV service!</p>
          <p><strong>Smartonn Team</strong></p>
        </div>
      </div>
    `,
    textContent: `Your IPTV Subscription Details - Order #{{orderId}}

Dear {{customerName}},

Here is your subscription as an M3U link:
{{m3uLink}}

———————————————

Username: {{username}}
Password: {{password}}
Lien: http://premium.pro4ott.com:8789
Lien 2: http://live.mypythontv.com:2052
Lien 3: http://mypythonpremium.com:2052

———————————————

Installation: https://smartonn.com/install/

———————————————

If you have any problems contact us at
Email: <EMAIL>

Thank you for choosing our IPTV service!
Smartonn Team`,
    category: 'iptv',
    isDefault: true
  },

  // Support Emails
  {
    id: 'affiliate_invitation',
    name: 'Affiliate Program Invitation',
    description: 'Invitation to join the affiliate program',
    subject: 'Join Our Affiliate Program - Earn Commissions!',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #333; margin-bottom: 10px;">Join Our Affiliate Program</h1>
          <p style="color: #666; font-size: 16px;">Earn commissions by referring new customers</p>
        </div>

        <div style="margin-bottom: 30px;">
          <p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>
          <p style="font-size: 16px; line-height: 1.5;">We're excited to invite you to join our affiliate program! As a valued customer, we believe you'd be a perfect partner to help us grow.</p>
        </div>

        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: center;">
          <h2 style="color: #e63946; font-size: 24px; margin-bottom: 10px;">Earn 20% Commission</h2>
          <p style="font-size: 16px; margin-bottom: 15px;">On every successful referral you make</p>
          <div style="margin-top: 15px;">
            <a href="{{affiliateLink}}" style="display: inline-block; padding: 10px 20px; background-color: #e63946; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">Join Now</a>
          </div>
        </div>

        <div style="margin-bottom: 30px;">
          <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Program Benefits:</h3>
          <ul style="padding-left: 20px;">
            <li style="margin-bottom: 5px;">20% commission on all referred sales</li>
            <li style="margin-bottom: 5px;">Monthly payments via PayPal</li>
            <li style="margin-bottom: 5px;">Access to marketing materials</li>
            <li style="margin-bottom: 5px;">Dedicated affiliate support</li>
          </ul>
        </div>

        <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
          <p style="margin: 0; color: #495057;">If you have any questions, please reply to this email.</p>
          <p style="margin: 5px 0 0 0; color: #495057;"><strong>Thank you,<br>Support Team</strong></p>
        </div>
      </div>
    `,
    textContent: 'Join Our Affiliate Program - Earn Commissions! Dear {{customerName}}, We are excited to invite you to join our affiliate program! As a valued customer, we believe you would be a perfect partner to help us grow. Earn 20% Commission on every successful referral you make. Program Benefits: 20% commission on all referred sales, Monthly payments via PayPal, Access to marketing materials, Dedicated affiliate support.',
    category: 'marketing',
    isDefault: true
  },

  {
    id: 'refund_confirmation',
    name: 'Refund Confirmation',
    description: 'Confirmation of a refund request',
    subject: 'Your Refund Request - Confirmation #{{orderNumber}}',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #333; margin-bottom: 10px;">Refund Confirmation</h1>
          <p style="color: #666; font-size: 16px;">Order #{{orderNumber}}</p>
        </div>

        <div style="margin-bottom: 30px;">
          <p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>
          <p style="font-size: 16px; line-height: 1.5;">We're writing to confirm that we've processed your refund request for the following order:</p>
        </div>

        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="color: #333; margin-top: 0;">Refund Details:</h3>
          <p><strong>Invoice Number:</strong> ` + `{{orderNumber}}` + `</p>
          <p><strong>Date:</strong> ` + `{{date}}` + `</p>
          <p><strong>Product:</strong> ` + `{{productName}}` + `</p>
          <p><strong>Amount Refunded:</strong> ` + `{{price}}` + `</p>
          <p><strong>Refund Method:</strong> Original payment method</p>
          <p><strong>Customer:</strong> ` + `{{customerName}}` + `</p>
        </div>

        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <p style="margin: 0; color: #856404;"><strong>Note:</strong> After order confirmation, you will receive your subscription by email within 8 hours (during working hours), but generally all subscriptions are sent within 3 hours of your order. Please check your spam folder if you cannot find the email.</p>
        </div>

        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <p style="margin: 5px 0;">The refund has been processed and should appear in your account within 5-10 business days, depending on your payment provider.</p>
          <p style="margin: 5px 0;">If you have any questions about this refund, please contact our support team.</p>
        </div>

        <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
          <p style="margin: 0; color: #495057;">If you have any questions, please reply to this email.</p>
          <p style="margin: 5px 0 0 0; color: #495057;"><strong>Thank you,<br>Support Team</strong></p>
        </div>
      </div>
    `,
    textContent: 'Refund Confirmation - Order #{{orderNumber}}. Dear {{customerName}}, We are writing to confirm that we have processed your refund request for the following order: Product: {{productName}}, Amount Refunded: {{price}}, Refund Date: {{date}}, Refund Method: Original payment method. The refund has been processed and should appear in your account within 5-10 business days, depending on your payment provider. If you have any questions about this refund, please contact our support team.',
    category: 'purchase',
    isDefault: true
  },

  {
    id: 'subscription_expiring',
    name: 'Subscription Expiring Soon',
    description: 'Notification that a subscription is about to expire',
    subject: 'Your Subscription is Expiring Soon - Action Required',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #333; margin-bottom: 10px;">Your Subscription is Expiring Soon</h1>
          <p style="color: #666; font-size: 16px;">Action Required to Maintain Access</p>
        </div>

        <div style="margin-bottom: 30px;">
          <p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>
          <p style="font-size: 16px; line-height: 1.5;">This is a friendly reminder that your subscription is set to expire soon. To ensure uninterrupted access to our services, please renew your subscription before the expiration date.</p>
        </div>

        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="color: #333; margin-top: 0;">Subscription Details:</h3>
          <p><strong>Product:</strong> ` + `{{productName}}` + `</p>
          <p><strong>Expiration Date:</strong> ` + `{{expiryDate}}` + `</p>
          <p><strong>Customer:</strong> ` + `{{customerName}}` + `</p>
        </div>

        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <p style="margin: 0; color: #856404;"><strong>Note:</strong> After order confirmation, you will receive your subscription by email within 8 hours (during working hours), but generally all subscriptions are sent within 3 hours of your order. Please check your spam folder if you cannot find the email.</p>
        </div>

        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: center;">
          <h3 style="color: #333; margin-top: 0;">Renew Now to Avoid Service Interruption</h3>
          <div style="margin-top: 15px;">
            <a href="{{renewalLink}}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">Renew Subscription</a>
          </div>
        </div>

        <div style="margin-bottom: 30px;">
          <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What Happens If You Don't Renew?</h3>
          <p style="margin: 5px 0;">If your subscription expires, you will lose access to:</p>
          <ul style="padding-left: 20px;">
            <li style="margin-bottom: 5px;">All premium content and features</li>
            <li style="margin-bottom: 5px;">Customer support</li>
            <li style="margin-bottom: 5px;">Future updates and improvements</li>
          </ul>
        </div>

        <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
          <p style="margin: 0; color: #495057;">If you have any questions, please reply to this email.</p>
          <p style="margin: 5px 0 0 0; color: #495057;"><strong>Thank you,<br>Support Team</strong></p>
        </div>
      </div>
    `,
    textContent: 'Your Subscription is Expiring Soon - Action Required. Dear {{customerName}}, This is a friendly reminder that your subscription is set to expire soon. To ensure uninterrupted access to our services, please renew your subscription before the expiration date. Product: {{productName}}, Expiration Date: {{expiryDate}}. What Happens If You Do Not Renew? If your subscription expires, you will lose access to: All premium content and features, Customer support, Future updates and improvements.',
    category: 'notification',
    isDefault: true
  },

  {
    id: 'support_ticket',
    name: 'Support Ticket Response',
    description: 'Response to a customer support ticket',
    subject: 'Re: Your Support Ticket #{{ticketNumber}}',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #333; margin-bottom: 10px;">Support Ticket Update</h1>
          <p style="color: #666; font-size: 16px;">Ticket #{{ticketNumber}}</p>
        </div>

        <div style="margin-bottom: 30px;">
          <p style="font-size: 16px; line-height: 1.5;">Dear {{customerName}},</p>
          <p style="font-size: 16px; line-height: 1.5;">Thank you for contacting our support team. We're writing in response to your recent support ticket.</p>
        </div>

        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="color: #333; margin-top: 0;">Your Question:</h3>
          <p style="font-style: italic;">{{customerQuestion}}</p>

          <h3 style="color: #333; margin-bottom: 10px;">Our Response:</h3>
          <p>{{supportResponse}}</p>
        </div>

        <div style="margin-bottom: 30px;">
          <p style="font-size: 16px; line-height: 1.5;">If you have any further questions or need additional assistance, please don't hesitate to reply to this email or open a new support ticket.</p>
          <p style="font-size: 16px; line-height: 1.5;">Thank you for choosing our services!</p>
        </div>

        <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
          <p style="margin: 0; color: #495057;">If you have any questions, please reply to this email.</p>
          <p style="margin: 5px 0 0 0; color: #495057;"><strong>Thank you,<br>Support Team</strong></p>
        </div>
      </div>
    `,
    textContent: 'Support Ticket Update. Ticket #{{ticketNumber}}. Dear {{customerName}}, Thank you for contacting our support team. We are writing in response to your recent support ticket. Your Question: {{customerQuestion}}. Our Response: {{supportResponse}}. If you have any further questions or need additional assistance, please do not hesitate to reply to this email or open a new support ticket. Thank you for choosing our services!',
    category: 'support',
    isDefault: true
  }
];
