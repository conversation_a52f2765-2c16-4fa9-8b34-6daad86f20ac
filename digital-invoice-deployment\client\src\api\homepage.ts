import { apiRequest } from '@/lib/queryClient';

// Types for homepage configuration
export interface HeroSection {
  title: string;
  subtitle: string;
  description: string;
  ctaText: string;
  ctaLink: string;
  backgroundImage: string;
  backgroundType: 'image' | 'gradient' | 'solid';
  backgroundColor: string;
  textColor: string;
  showVideo: boolean;
  videoUrl: string;
}

export interface FeatureItem {
  id: string;
  icon: string;
  title: string;
  description: string;
  enabled: boolean;
}

export interface FeaturesSection {
  title: string;
  subtitle: string;
  features: FeatureItem[];
  layout: 'grid' | 'list' | 'carousel';
  columns: number;
}

export interface TestimonialItem {
  id: string;
  name: string;
  role: string;
  company: string;
  content: string;
  avatar: string;
  rating: number;
  enabled: boolean;
}

export interface TestimonialsSection {
  title: string;
  subtitle: string;
  testimonials: TestimonialItem[];
  layout: 'carousel' | 'grid';
  showRatings: boolean;
}

export interface ProductsSection {
  title: string;
  subtitle: string;
  showAllProducts: boolean;
  featuredProductIds: number[];
  layout: 'grid' | 'carousel';
  columns: number;
  showPrices: boolean;
  showDescriptions: boolean;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  enabled: boolean;
}

export interface FAQSection {
  title: string;
  subtitle: string;
  faqs: FAQItem[];
  layout: 'accordion' | 'tabs';
}

export interface CTASection {
  title: string;
  description: string;
  primaryButtonText: string;
  primaryButtonLink: string;
  secondaryButtonText: string;
  secondaryButtonLink: string;
  backgroundType: 'image' | 'gradient' | 'solid';
  backgroundColor: string;
  backgroundImage: string;
  textColor: string;
}

export interface PageSection {
  id: string;
  type: 'hero' | 'features' | 'testimonials' | 'products' | 'faq' | 'cta';
  title: string;
  enabled: boolean;
  order: number;
  content: HeroSection | FeaturesSection | TestimonialsSection | ProductsSection | FAQSection | CTASection;
}

export interface SEOSettings {
  title: string;
  description: string;
  keywords: string;
  ogTitle: string;
  ogDescription: string;
  ogImage: string;
  twitterTitle: string;
  twitterDescription: string;
  twitterImage: string;
}

export interface ThemeSettings {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  borderRadius: string;
  spacing: string;
}

export interface HomepageConfig {
  sections: PageSection[];
  seo: SEOSettings;
  theme: ThemeSettings;
  lastUpdated: string;
  version: number;
}

// API functions
export const getHomepageConfig = async (): Promise<HomepageConfig> => {
  return apiRequest('/api/homepage', 'GET');
};

export const updateHomepageConfig = async (config: Partial<HomepageConfig>): Promise<HomepageConfig> => {
  return apiRequest('/api/homepage', 'PUT', config);
};

export const updateSEOSettings = async (seo: SEOSettings): Promise<HomepageConfig> => {
  return apiRequest('/api/homepage/seo', 'PUT', seo);
};

export const updateThemeSettings = async (theme: ThemeSettings): Promise<HomepageConfig> => {
  return apiRequest('/api/homepage/theme', 'PUT', theme);
};

export const updateSection = async (sectionId: string, section: PageSection): Promise<HomepageConfig> => {
  return apiRequest(`/api/homepage/sections/${sectionId}`, 'PUT', section);
};

export const addSection = async (section: PageSection): Promise<HomepageConfig> => {
  return apiRequest('/api/homepage/sections', 'POST', section);
};

export const removeSection = async (sectionId: string): Promise<HomepageConfig> => {
  return apiRequest(`/api/homepage/sections/${sectionId}`, 'DELETE');
};

export const reorderSections = async (sectionIds: string[]): Promise<HomepageConfig> => {
  return apiRequest('/api/homepage/sections/reorder', 'PUT', { sectionIds });
};

export const resetHomepageConfig = async (): Promise<HomepageConfig> => {
  return apiRequest('/api/homepage/reset', 'POST');
};

// Helper functions for creating new sections
export const createHeroSection = (id: string, order: number): PageSection => ({
  id,
  type: 'hero',
  title: 'Hero Section',
  enabled: true,
  order,
  content: {
    title: 'Your Amazing Title',
    subtitle: 'Compelling Subtitle',
    description: 'Describe your amazing product or service here.',
    ctaText: 'Get Started',
    ctaLink: '#products',
    backgroundImage: '',
    backgroundType: 'gradient',
    backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    textColor: '#ffffff',
    showVideo: false,
    videoUrl: ''
  } as HeroSection
});

export const createFeaturesSection = (id: string, order: number): PageSection => ({
  id,
  type: 'features',
  title: 'Features Section',
  enabled: true,
  order,
  content: {
    title: 'Amazing Features',
    subtitle: 'Everything you need to succeed',
    features: [
      {
        id: `feature-${Date.now()}-1`,
        icon: '⭐',
        title: 'Feature 1',
        description: 'Description of your first amazing feature.',
        enabled: true
      },
      {
        id: `feature-${Date.now()}-2`,
        icon: '🚀',
        title: 'Feature 2',
        description: 'Description of your second amazing feature.',
        enabled: true
      },
      {
        id: `feature-${Date.now()}-3`,
        icon: '💎',
        title: 'Feature 3',
        description: 'Description of your third amazing feature.',
        enabled: true
      }
    ],
    layout: 'grid',
    columns: 3
  } as FeaturesSection
});

export const createProductsSection = (id: string, order: number): PageSection => ({
  id,
  type: 'products',
  title: 'Products Section',
  enabled: true,
  order,
  content: {
    title: 'Our Products',
    subtitle: 'Choose from our amazing selection',
    showAllProducts: true,
    featuredProductIds: [],
    layout: 'grid',
    columns: 3,
    showPrices: true,
    showDescriptions: true
  } as ProductsSection
});

export const createCTASection = (id: string, order: number): PageSection => ({
  id,
  type: 'cta',
  title: 'Call to Action',
  enabled: true,
  order,
  content: {
    title: 'Ready to Get Started?',
    description: 'Join thousands of satisfied customers today.',
    primaryButtonText: 'Get Started',
    primaryButtonLink: '#products',
    secondaryButtonText: 'Learn More',
    secondaryButtonLink: '#features',
    backgroundType: 'solid',
    backgroundColor: '#f8fafc',
    backgroundImage: '',
    textColor: '#1e293b'
  } as CTASection
});

// Section type helpers
export const getSectionTypeLabel = (type: PageSection['type']): string => {
  const labels = {
    hero: 'Hero Section',
    features: 'Features',
    testimonials: 'Testimonials',
    products: 'Products',
    faq: 'FAQ',
    cta: 'Call to Action'
  };
  return labels[type] || type;
};

export const getSectionIcon = (type: PageSection['type']): string => {
  const icons = {
    hero: '🎯',
    features: '⭐',
    testimonials: '💬',
    products: '🛍️',
    faq: '❓',
    cta: '🚀'
  };
  return icons[type] || '📄';
};
