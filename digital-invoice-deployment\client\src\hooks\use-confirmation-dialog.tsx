import * as React from "react";
import {
  Alert<PERSON><PERSON>og,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON>ert<PERSON><PERSON>og<PERSON>eader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

type ConfirmationDialogState = {
  open: boolean;
  title: React.ReactNode;
  description: React.ReactNode;
  onConfirm: () => void;
  confirmText: string;
  cancelText: string;
};

type ConfirmationDialogAction = 
  | { 
      type: "OPEN_CONFIRMATION_DIALOG"; 
      title: React.ReactNode; 
      description: React.ReactNode;
      onConfirm: () => void;
      confirmText?: string;
      cancelText?: string;
    }
  | { type: "CLOSE_CONFIRMATION_DIALOG" };

type ConfirmationDialogDispatch = (action: ConfirmationDialogAction) => void;

const ConfirmationDialogContext = React.createContext<
  { state: ConfirmationD<PERSON>ogState; dispatch: ConfirmationDialogDispatch } | undefined
>(undefined);

function confirmationDialogReducer(
  state: ConfirmationDialogState, 
  action: ConfirmationDialogAction
): ConfirmationDialogState {
  switch (action.type) {
    case "OPEN_CONFIRMATION_DIALOG":
      return {
        ...state,
        open: true,
        title: action.title,
        description: action.description,
        onConfirm: action.onConfirm,
        confirmText: action.confirmText || "Confirm",
        cancelText: action.cancelText || "Cancel",
      };
    case "CLOSE_CONFIRMATION_DIALOG":
      return {
        ...state,
        open: false,
      };
    default:
      return state;
  }
}

export function ConfirmationDialogProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = React.useReducer(confirmationDialogReducer, {
    open: false,
    title: "",
    description: "",
    onConfirm: () => {},
    confirmText: "Confirm",
    cancelText: "Cancel",
  });

  return (
    <ConfirmationDialogContext.Provider value={{ state, dispatch }}>
      {children}
      <ConfirmationDialogComponent />
    </ConfirmationDialogContext.Provider>
  );
}

function ConfirmationDialogComponent() {
  const context = React.useContext(ConfirmationDialogContext);
  if (!context) {
    throw new Error("useConfirmationDialog must be used within a ConfirmationDialogProvider");
  }
  
  const { state, dispatch } = context;

  const handleConfirm = () => {
    state.onConfirm();
    dispatch({ type: "CLOSE_CONFIRMATION_DIALOG" });
  };

  return (
    <AlertDialog open={state.open} onOpenChange={(open) => {
      if (!open) dispatch({ type: "CLOSE_CONFIRMATION_DIALOG" });
    }}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{state.title}</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="text-sm text-muted-foreground">
              {state.description}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>
            {state.cancelText}
          </AlertDialogCancel>
          <AlertDialogAction onClick={handleConfirm}>
            {state.confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export function useConfirmationDialog() {
  const context = React.useContext(ConfirmationDialogContext);
  if (!context) {
    throw new Error("useConfirmationDialog must be used within a ConfirmationDialogProvider");
  }
  
  const { dispatch } = context;

  const showConfirmation = React.useCallback(
    (
      title: React.ReactNode, 
      description: React.ReactNode, 
      onConfirm: () => void,
      options?: { confirmText?: string; cancelText?: string }
    ) => {
      dispatch({
        type: "OPEN_CONFIRMATION_DIALOG",
        title,
        description,
        onConfirm,
        confirmText: options?.confirmText,
        cancelText: options?.cancelText,
      });
    },
    [dispatch]
  );

  return { showConfirmation };
}
