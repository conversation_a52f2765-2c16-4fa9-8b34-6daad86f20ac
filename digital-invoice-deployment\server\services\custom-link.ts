import { Product, CheckoutData } from '../../shared/schema';
import { storage } from '../storage-factory';

/**
 * Interface for the custom payment link result
 */
export interface CustomLinkResult {
  id: string;
  url: string;
  isSimulated?: boolean;
  error?: string;
  status?: string;
  linkName?: string;
  buttonText?: string;
}

/**
 * Test a specific custom payment link configuration
 */
export async function testCustomLinkConnection(config: any): Promise<boolean> {
  try {
    // If testing a single link
    if (config.paymentLink) {
      // Validate the payment link
      const { paymentLink, buttonText } = config;

      if (!paymentLink || !buttonText) {
        console.error('Custom payment link configuration is incomplete');
        return false;
      }

      // Check if the payment link is a valid URL
      try {
        new URL(paymentLink);
      } catch (error) {
        console.error('Invalid payment link URL:', error);
        return false;
      }

      return true;
    }
    // If testing the entire configuration with multiple links
    else if (config.links && Array.isArray(config.links)) {
      // Check if there are any active links
      const activeLinks = config.links.filter((link: any) => link.active);
      if (activeLinks.length === 0) {
        console.error('No active payment links found');
        return false;
      }

      // Validate each active link
      for (const link of activeLinks) {
        if (!link.paymentLink || !link.buttonText) {
          console.error(`Link "${link.name}" is incomplete`);
          return false;
        }

        try {
          new URL(link.paymentLink);
        } catch (error) {
          console.error(`Invalid URL for link "${link.name}":`, error);
          return false;
        }
      }

      return true;
    } else {
      console.error('Invalid custom payment link configuration');
      return false;
    }
  } catch (error) {
    console.error('Error testing custom payment link:', error);
    return false;
  }
}

/**
 * Get the next payment link to use based on the rotation method
 */
export function getNextPaymentLink(config: any): any {
  // Get all active links
  const activeLinks = config.links.filter((link: any) => link.active);

  if (activeLinks.length === 0) {
    throw new Error('No active payment links found');
  }

  // If there's only one active link, use it
  if (activeLinks.length === 1) {
    return activeLinks[0];
  }

  // Use the specified rotation method
  switch (config.rotationMethod) {
    case 'random':
      // Randomly select a link
      const randomIndex = Math.floor(Math.random() * activeLinks.length);
      return activeLinks[randomIndex];

    case 'round-robin':
    default:
      // Get the next link in sequence
      let nextIndex = (config.lastUsedIndex || 0) + 1;
      if (nextIndex >= activeLinks.length) {
        nextIndex = 0;
      }

      // Update the last used index
      config.lastUsedIndex = nextIndex;

      return activeLinks[nextIndex];
  }
}

/**
 * Create a custom payment link for a customer
 */
export async function createCustomPaymentLink(
  customerData: CheckoutData,
  product: Product,
  isTrial: boolean = false
): Promise<CustomLinkResult> {
  try {
    // Get the custom link configuration
    const paymentConfig = await storage.getPaymentConfig();
    const providerId = isTrial ? 'trial-custom-link' : 'custom-link';
    const customLinkProvider = paymentConfig.providers.find(p => p.id === providerId);

    if (!customLinkProvider) {
      console.error(`${isTrial ? 'Trial custom' : 'Custom'} payment link provider not found in configuration`);
      return {
        id: `SIMULATED-${Date.now()}`,
        url: '#',
        isSimulated: true,
        error: `${isTrial ? 'Trial custom' : 'Custom'} payment link provider is not configured`
      };
    }

    if (!customLinkProvider.active) {
      console.error(`${isTrial ? 'Trial custom' : 'Custom'} payment link provider is not active`);
      return {
        id: `SIMULATED-${Date.now()}`,
        url: '#',
        isSimulated: true,
        error: `${isTrial ? 'Trial custom' : 'Custom'} payment link provider is not active. Please activate it in Payment Settings.`
      };
    }

    const config = customLinkProvider.config as any;

    // Validate the configuration
    if (!config.links || !Array.isArray(config.links) || config.links.length === 0) {
      return {
        id: `SIMULATED-${Date.now()}`,
        url: '#',
        isSimulated: true,
        error: 'No payment links configured'
      };
    }

    // Get the next payment link to use
    let selectedLink;
    try {
      selectedLink = getNextPaymentLink(config);
    } catch (error) {
      return {
        id: `SIMULATED-${Date.now()}`,
        url: '#',
        isSimulated: true,
        error: error instanceof Error ? error.message : 'Error selecting payment link'
      };
    }

    // Generate a unique ID for this payment
    const paymentId = `CUSTOM-${Date.now()}`;

    // Construct the payment URL with query parameters
    let paymentUrl = selectedLink.paymentLink;

    // Add query parameters if the URL doesn't already have them
    const separator = paymentUrl.includes('?') ? '&' : '?';

    // Add customer and product information as query parameters
    paymentUrl += `${separator}paymentId=${paymentId}&customerName=${encodeURIComponent(customerData.fullName)}&customerEmail=${encodeURIComponent(customerData.email)}&productId=${customerData.productId}&productName=${encodeURIComponent(product.name)}&amount=${product.price}`;

    return {
      id: paymentId,
      url: paymentUrl,
      status: 'pending',
      linkName: selectedLink.name,
      buttonText: selectedLink.buttonText
    };
  } catch (error) {
    console.error('Error creating custom payment link:', error);
    return {
      id: `ERROR-${Date.now()}`,
      url: '#',
      isSimulated: true,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
