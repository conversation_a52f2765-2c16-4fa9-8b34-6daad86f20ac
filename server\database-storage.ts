import { db } from './db';
import { eq, and } from 'drizzle-orm';
import Database from 'better-sqlite3';
import {
  users,
  products,
  invoices,
  customCheckoutPages,
  allowedEmails,
  emailTemplates,
  paypalButtons,
  customInvoices,
  smtpProviders,
  customPaymentLinks,
  generalSettings,
  homepageConfig,
  systemMessages,
  devices,
  recoveryCodes,
  insertEmailTemplateSchema,
  type User,
  type InsertUser,
  type Product,
  type InsertProduct,
  type Invoice,
  type InsertInvoice,
  type CustomCheckoutPage,
  type InsertCustomCheckoutPage,
  type AllowedEmail,
  type InsertAllowedEmail,
  type EmailTemplate,
  type InsertEmailTemplate,
  type PaypalButton,
  type InsertPaypalButton,
  type CustomInvoice,
  type InsertCustomInvoice,
  type SmtpProvider,
  type InsertSmtpProvider,
  type CustomPaymentLink,
  type InsertCustomPaymentLink,
  type GeneralSettingsRecord,
  type InsertGeneralSettings,
  type HomepageConfigRecord,
  type InsertHomepageConfig,
  type SystemMessageRecord,
  type InsertSystemMessage,
  type Device,
  type InsertDevice,
  type RecoveryCode,
  type InsertRecoveryCode
} from '@shared/schema';
import { EmbedCode } from '@shared/embed-codes';
import { IStorage } from './storage-interface';
import { createHash } from 'crypto';



export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    try {
      const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
      const user = result[0];
      if (!user) return undefined;
      
      // Get devices and recovery codes
      const userDevices = await this.getDevices(user.id);
      const userRecoveryCodes = await this.getRecoveryCodes(user.id);

      return {
        ...user,
        rememberMe: false,
        resetToken: undefined,
        resetTokenExpiry: undefined,
        twoFactorSecret: user.twoFactorSecret || undefined,
        twoFactorEnabled: user.twoFactorEnabled || false,
        recoveryCodes: userRecoveryCodes,
        devices: userDevices
      };
    } catch (error) {
      console.error('Error getting user:', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
      const user = result[0];
      if (!user) return undefined;

      // Get devices and recovery codes
      const userDevices = await this.getDevices(user.id);
      const userRecoveryCodes = await this.getRecoveryCodes(user.id);

      return {
        ...user,
        rememberMe: false,
        resetToken: undefined,
        resetTokenExpiry: undefined,
        twoFactorSecret: user.twoFactorSecret || undefined,
        twoFactorEnabled: user.twoFactorEnabled || false,
        recoveryCodes: userRecoveryCodes,
        devices: userDevices
      };
    } catch (error) {
      console.error('Error getting user by username:', error);
      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      let password = insertUser.password;
      if (!password.match(/^[0-9a-f]{64}$/i)) {
        password = createHash('sha256').update(password).digest('hex');
      }

      const now = new Date().toISOString();
      const userData = {
        ...insertUser,
        password,
        createdAt: now,
        updatedAt: now
      };
      const result = await db.insert(users).values(userData).returning();
      const user = result[0];

      return {
        ...user,
        rememberMe: false,
        resetToken: undefined,
        resetTokenExpiry: undefined,
        twoFactorSecret: user.twoFactorSecret || undefined,
        twoFactorEnabled: user.twoFactorEnabled || false,
        recoveryCodes: [],
        devices: []
      };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  async verifyUserCredentials(username: string, password: string): Promise<boolean> {
    const user = await this.getUserByUsername(username);
    if (!user) return false;
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean> {
    if (!user) return false;
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async updateUser(userId: number, updates: Partial<InsertUser>): Promise<User | undefined> {
    try {
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      const result = await db.update(users)
        .set(updateData)
        .where(eq(users.id, userId))
        .returning();

      if (result.length === 0) {
        return undefined;
      }

      // Return the updated user with full data
      return await this.getUser(userId);
    } catch (error) {
      console.error('Error updating user:', error);
      return undefined;
    }
  }

  // Product methods
  async getProducts(): Promise<Product[]> {
    try {
      return await db.select().from(products);
    } catch (error) {
      console.error('Error getting products:', error);
      return [];
    }
  }

  async getProduct(id: number): Promise<Product | undefined> {
    try {
      const result = await db.select().from(products).where(eq(products.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting product:', error);
      return undefined;
    }
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    try {
      // Convert data for SQLite compatibility
      const sqliteData = {
        ...insertProduct,
        // Convert boolean to integer for SQLite
        active: insertProduct.active ? 1 : 0,
        // Ensure price is a string
        price: typeof insertProduct.price === 'number' ? insertProduct.price.toString() : insertProduct.price
      };

      const result = await db.insert(products).values(sqliteData).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  // Invoice methods
  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    try {
      // Convert data for SQLite compatibility
      const sqliteData = {
        ...insertInvoice,
        // Convert booleans to integers for SQLite
        isTrialOrder: insertInvoice.isTrialOrder ? 1 : 0,
        hasUpgraded: insertInvoice.hasUpgraded ? 1 : 0,
        // Ensure amount is a string
        amount: typeof insertInvoice.amount === 'number' ? insertInvoice.amount.toString() : insertInvoice.amount
      };

      const result = await db.insert(invoices).values(sqliteData).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  }

  async getInvoice(id: number): Promise<Invoice | undefined> {
    try {
      const result = await db.select().from(invoices).where(eq(invoices.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting invoice:', error);
      return undefined;
    }
  }

  async getInvoices(): Promise<Invoice[]> {
    try {
      return await db.select().from(invoices);
    } catch (error) {
      console.error('Error getting invoices:', error);
      return [];
    }
  }

  async updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    try {
      // Convert data for SQLite compatibility
      const sqliteUpdate: any = { ...update };

      // Convert booleans to integers for SQLite
      if ('isTrialOrder' in update) {
        sqliteUpdate.isTrialOrder = update.isTrialOrder ? 1 : 0;
      }
      if ('hasUpgraded' in update) {
        sqliteUpdate.hasUpgraded = update.hasUpgraded ? 1 : 0;
      }
      // Ensure amount is a string
      if ('amount' in update && typeof update.amount === 'number') {
        sqliteUpdate.amount = update.amount.toString();
      }

      const result = await db.update(invoices).set(sqliteUpdate).where(eq(invoices.id, id)).returning();
      return result[0];
    } catch (error) {
      console.error('Error updating invoice:', error);
      return undefined;
    }
  }

  async deleteInvoice(id: number): Promise<boolean> {
    try {
      const result = await db.delete(invoices).where(eq(invoices.id, id));
      console.log('Delete invoice result:', result);
      // For SQLite, check if changes property exists, otherwise assume success if no error
      return result.changes ? result.changes > 0 : true;
    } catch (error) {
      console.error('Error deleting invoice:', error);
      return false;
    }
  }

  // Custom Checkout Page methods
  async createCustomCheckoutPage(insertPage: InsertCustomCheckoutPage): Promise<CustomCheckoutPage> {
    try {
      // Convert data for SQLite compatibility
      const sqliteData = {
        ...insertPage,
        // Convert booleans to integers for SQLite
        requireAllowedEmail: insertPage.requireAllowedEmail ? 1 : 0,
        isTrialCheckout: insertPage.isTrialCheckout ? 1 : 0,
        useReferrerMasking: insertPage.useReferrerMasking ? 1 : 0,
        active: insertPage.active ? 1 : 0,
        // Ensure price is a string
        price: typeof insertPage.price === 'number' ? insertPage.price.toString() : insertPage.price,
        // Ensure views and conversions are numbers
        views: insertPage.views || 0,
        conversions: insertPage.conversions || 0
      };

      const result = await db.insert(customCheckoutPages).values(sqliteData).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating custom checkout page:', error);
      throw error;
    }
  }

  async getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await db.select().from(customCheckoutPages).where(eq(customCheckoutPages.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await db.select().from(customCheckoutPages).where(eq(customCheckoutPages.slug, slug)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page by slug:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPages(): Promise<CustomCheckoutPage[]> {
    try {
      return await db.select().from(customCheckoutPages);
    } catch (error) {
      console.error('Error getting custom checkout pages:', error);
      return [];
    }
  }

  async updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined> {
    try {
      // Convert data for SQLite compatibility
      const sqliteUpdate: any = { ...update };

      // Convert booleans to integers for SQLite
      if ('requireAllowedEmail' in update) {
        sqliteUpdate.requireAllowedEmail = update.requireAllowedEmail ? 1 : 0;
      }
      if ('isTrialCheckout' in update) {
        sqliteUpdate.isTrialCheckout = update.isTrialCheckout ? 1 : 0;
      }
      if ('useReferrerMasking' in update) {
        sqliteUpdate.useReferrerMasking = update.useReferrerMasking ? 1 : 0;
      }
      if ('active' in update) {
        sqliteUpdate.active = update.active ? 1 : 0;
      }
      // Ensure price is a string
      if ('price' in update && typeof update.price === 'number') {
        sqliteUpdate.price = update.price.toString();
      }

      const result = await db.update(customCheckoutPages).set(sqliteUpdate).where(eq(customCheckoutPages.id, id)).returning();
      return result[0];
    } catch (error) {
      console.error('Error updating custom checkout page:', error);
      return undefined;
    }
  }

  async incrementCustomCheckoutPageViews(id: number): Promise<void> {
    try {
      const current = await this.getCustomCheckoutPage(id);
      if (current) {
        await db.update(customCheckoutPages)
          .set({ views: (current.views || 0) + 1 })
          .where(eq(customCheckoutPages.id, id));
      }
    } catch (error) {
      console.error('Error incrementing views:', error);
    }
  }

  async incrementCustomCheckoutPageConversions(id: number): Promise<void> {
    try {
      const current = await this.getCustomCheckoutPage(id);
      if (current) {
        await db.update(customCheckoutPages)
          .set({ conversions: (current.conversions || 0) + 1 })
          .where(eq(customCheckoutPages.id, id));
      }
    } catch (error) {
      console.error('Error incrementing conversions:', error);
    }
  }

  async deleteCustomCheckoutPage(id: number): Promise<boolean> {
    try {
      const result = await db.delete(customCheckoutPages).where(eq(customCheckoutPages.id, id));
      return result.rowsAffected > 0;
    } catch (error) {
      console.error('Error deleting custom checkout page:', error);
      return false;
    }
  }

  // Allowed Email methods
  async getAllowedEmails(): Promise<AllowedEmail[]> {
    try {
      return await db.select().from(allowedEmails);
    } catch (error) {
      console.error('Error getting allowed emails:', error);
      return [];
    }
  }

  async getAllowedEmail(id: number): Promise<AllowedEmail | undefined> {
    try {
      const result = await db.select().from(allowedEmails).where(eq(allowedEmails.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting allowed email:', error);
      return undefined;
    }
  }

  async getEmailByAddress(email: string): Promise<AllowedEmail | undefined> {
    try {
      const result = await db.select().from(allowedEmails).where(eq(allowedEmails.email, email.toLowerCase())).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting email by address:', error);
      return undefined;
    }
  }

  async isEmailAllowed(email: string): Promise<boolean> {
    try {
      const result = await db.select().from(allowedEmails).where(eq(allowedEmails.email, email.toLowerCase())).limit(1);
      return result.length > 0;
    } catch (error) {
      console.error('Error checking if email is allowed:', error);
      return false;
    }
  }

  async createAllowedEmail(insertAllowedEmail: InsertAllowedEmail): Promise<AllowedEmail> {
    try {
      const result = await db.insert(allowedEmails).values(insertAllowedEmail).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating allowed email:', error);
      throw error;
    }
  }

  async updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined> {
    try {
      const result = await db.update(allowedEmails).set(update).where(eq(allowedEmails.id, id)).returning();
      return result[0];
    } catch (error) {
      console.error('Error updating allowed email:', error);
      return undefined;
    }
  }

  async updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail> {
    try {
      const existing = await this.getEmailByAddress(emailAddress);
      if (existing) {
        const updated = await this.updateAllowedEmail(existing.id, update);
        return updated!;
      } else {
        return await this.createAllowedEmail({
          email: emailAddress,
          notes: update.notes || '',
          lastSubject: update.lastSubject,
          smtpProvider: update.smtpProvider,
          lastUpdated: update.lastUpdated || new Date().toISOString(),
          createdAt: update.createdAt || new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Error updating or creating allowed email:', error);
      throw error;
    }
  }

  async deleteAllowedEmail(id: number): Promise<boolean> {
    try {
      const result = await db.delete(allowedEmails).where(eq(allowedEmails.id, id));
      console.log('Delete result:', result);
      // For SQLite, check if changes property exists, otherwise assume success if no error
      return result.changes ? result.changes > 0 : true;
    } catch (error) {
      console.error('Error deleting allowed email:', error);
      return false;
    }
  }

  async bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const email of emails) {
      if (!email.trim()) {
        failed++;
        continue;
      }

      try {
        const exists = await this.isEmailAllowed(email.trim());
        if (exists) {
          failed++;
          continue;
        }

        await this.createAllowedEmail({
          email: email.trim(),
          notes: "Bulk imported",
          lastSubject: "",
          smtpProvider: "",
          lastUpdated: new Date().toISOString(),
          createdAt: new Date().toISOString()
        });
        success++;
      } catch (error) {
        failed++;
      }
    }

    return { success, failed };
  }

  // Placeholder methods for features not yet implemented
  async saveResetToken(userId: number, token: string, expiry: Date): Promise<void> {}
  async validateResetToken(token: string): Promise<boolean> { return false; }
  async getUserByResetToken(token: string): Promise<User | undefined> { return undefined; }
  async updateUserPassword(userId: number, password: string): Promise<void> {}
  async clearResetToken(userId: number): Promise<void> {}
  async updateUsername(userId: number, username: string): Promise<void> {}
  async updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void> {}
  async enableTwoFactor(userId: number, secret: string): Promise<void> {
    try {
      await db.update(users)
        .set({
          twoFactorSecret: secret,
          twoFactorEnabled: 1, // SQLite uses 1 for true
          updatedAt: new Date().toISOString()
        })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error enabling two factor:', error);
      throw error;
    }
  }

  async disableTwoFactor(userId: number): Promise<void> {
    try {
      await db.update(users)
        .set({
          twoFactorSecret: null,
          twoFactorEnabled: 0, // SQLite uses 0 for false
          updatedAt: new Date().toISOString()
        })
        .where(eq(users.id, userId));

      // Also remove all recovery codes
      await db.delete(recoveryCodes).where(eq(recoveryCodes.userId, userId));
    } catch (error) {
      console.error('Error disabling two factor:', error);
      throw error;
    }
  }

  async verifyTwoFactorToken(userId: number, token: string): Promise<boolean> {
    try {
      const user = await this.getUser(userId);
      if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) {
        return false;
      }

      const { authenticator } = await import('otplib');
      return authenticator.verify({ token, secret: user.twoFactorSecret });
    } catch (error) {
      console.error('Error verifying two factor token:', error);
      return false;
    }
  }

  async generateRecoveryCodes(userId: number): Promise<string[]> {
    try {
      // Remove existing recovery codes
      await db.delete(recoveryCodes).where(eq(recoveryCodes.userId, userId));

      // Generate 10 new recovery codes
      const codes: string[] = [];
      const crypto = await import('crypto');

      for (let i = 0; i < 10; i++) {
        const code = crypto.randomBytes(4).toString('hex').toUpperCase();
        codes.push(code);

        await db.insert(recoveryCodes).values({
          userId,
          code,
          used: 0, // SQLite uses 0 for false
          createdAt: new Date().toISOString()
        });
      }

      return codes;
    } catch (error) {
      console.error('Error generating recovery codes:', error);
      throw error;
    }
  }

  async verifyRecoveryCode(userId: number, code: string): Promise<boolean> {
    try {
      const result = await db.select()
        .from(recoveryCodes)
        .where(
          and(
            eq(recoveryCodes.userId, userId),
            eq(recoveryCodes.code, code.toUpperCase()),
            eq(recoveryCodes.used, 0) // SQLite uses 0 for false
          )
        )
        .limit(1);

      if (result.length === 0) {
        return false;
      }

      // Mark the code as used
      await db.update(recoveryCodes)
        .set({
          used: 1, // SQLite uses 1 for true
          usedAt: new Date().toISOString()
        })
        .where(eq(recoveryCodes.id, result[0].id));

      return true;
    } catch (error) {
      console.error('Error verifying recovery code:', error);
      return false;
    }
  }

  async addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device> {
    try {
      const crypto = await import('crypto');
      const deviceId = crypto.randomUUID();
      const now = new Date().toISOString();

      const deviceData = {
        id: deviceId,
        userId,
        name: deviceInfo.name,
        ip: deviceInfo.ip,
        userAgent: deviceInfo.userAgent,
        lastLogin: now,
        createdAt: now
      };

      await db.insert(devices).values(deviceData);
      return deviceData;
    } catch (error) {
      console.error('Error adding device:', error);
      throw error;
    }
  }

  async getDevices(userId: number): Promise<Device[]> {
    try {
      return await db.select()
        .from(devices)
        .where(eq(devices.userId, userId))
        .orderBy(devices.lastLogin);
    } catch (error) {
      console.error('Error getting devices:', error);
      return [];
    }
  }

  async updateDeviceLastLogin(userId: number, deviceId: string): Promise<void> {
    try {
      await db.update(devices)
        .set({ lastLogin: new Date().toISOString() })
        .where(
          and(
            eq(devices.userId, userId),
            eq(devices.id, deviceId)
          )
        );
    } catch (error) {
      console.error('Error updating device last login:', error);
    }
  }

  async removeDevice(userId: number, deviceId: string): Promise<boolean> {
    try {
      const result = await db.delete(devices)
        .where(
          and(
            eq(devices.userId, userId),
            eq(devices.id, deviceId)
          )
        );

      return result.changes ? result.changes > 0 : true;
    } catch (error) {
      console.error('Error removing device:', error);
      return false;
    }
  }

  async getRecoveryCodes(userId: number): Promise<Array<{ code: string; used: boolean }>> {
    try {
      const codes = await db.select()
        .from(recoveryCodes)
        .where(eq(recoveryCodes.userId, userId))
        .orderBy(recoveryCodes.createdAt);

      return codes.map(code => ({
        code: code.code,
        used: code.used || false
      }));
    } catch (error) {
      console.error('Error getting recovery codes:', error);
      return [];
    }
  }



  async getEmailConfig(): Promise<any> {
    // Use raw SQL to get SMTP providers
    const sqlite = new Database('data.db');
    const providers = sqlite.prepare('SELECT * FROM smtp_providers ORDER BY is_default DESC, name ASC').all();
    sqlite.close();

    return {
      providers: providers.map((provider: any) => ({
        id: provider.id,
        name: provider.name,
        active: provider.active === 1,
        isDefault: provider.is_default === 1,
        isBackup: provider.is_backup === 1,
        credentials: {
          host: provider.host,
          port: provider.port,
          secure: provider.secure === 1,
          auth: {
            user: provider.auth_user,
            pass: provider.auth_pass
          },
          fromEmail: provider.from_email,
          fromName: provider.from_name
        }
      }))
    };
  }

  async getPaymentConfig(): Promise<any> {
    // Use raw SQL to get custom payment links
    const sqlite = new Database('data.db');
    const regularLinks = sqlite.prepare('SELECT * FROM custom_payment_links WHERE is_trial_link = 0 ORDER BY name ASC').all();
    const trialLinks = sqlite.prepare('SELECT * FROM custom_payment_links WHERE is_trial_link = 1 ORDER BY name ASC').all();
    sqlite.close();

    return {
      providers: [
        {
          id: 'paypal',
          name: 'PayPal',
          active: true,
          config: {
            clientId: '********',
            clientSecret: '********',
            mode: 'sandbox',
            webhookId: '',
            paypalEmail: '<EMAIL>'
          }
        },
        {
          id: 'custom-link',
          name: 'Custom Payment Links',
          active: true,
          config: {
            links: regularLinks.map((link: any) => ({
              id: link.id,
              name: link.name,
              paymentLink: link.payment_link,
              buttonText: link.button_text,
              successRedirectUrl: link.success_redirect_url,
              active: link.active === 1
            })),
            rotationMethod: 'round-robin',
            lastUsedIndex: 0
          }
        },
        {
          id: 'trial-custom-link',
          name: 'Trial Custom Payment Links',
          active: true,
          config: {
            links: trialLinks.map((link: any) => ({
              id: link.id,
              name: link.name,
              paymentLink: link.payment_link,
              buttonText: link.button_text,
              successRedirectUrl: link.success_redirect_url,
              active: link.active === 1
            })),
            rotationMethod: 'round-robin',
            lastUsedIndex: 0
          }
        }
      ]
    };
  }

  // SMTP Provider methods
  async createSmtpProvider(data: InsertSmtpProvider): Promise<SmtpProvider> {
    const sqlite = new Database('data.db');

    const sql = `
      INSERT INTO smtp_providers (
        id, name, host, port, secure, auth_user, auth_pass,
        from_email, from_name, active, is_default, is_backup,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      data.id,
      data.name,
      data.host,
      data.port,
      data.secure ? 1 : 0,
      data.authUser,
      data.authPass,
      data.fromEmail,
      data.fromName,
      data.active ? 1 : 0,
      data.isDefault ? 1 : 0,
      data.isBackup ? 1 : 0,
      data.createdAt,
      data.updatedAt
    ];

    const stmt = sqlite.prepare(sql);
    const result = stmt.run(...values);

    const selectStmt = sqlite.prepare('SELECT * FROM smtp_providers WHERE id = ?');
    const provider = selectStmt.get(data.id);

    sqlite.close();
    return provider as SmtpProvider;
  }

  async getSmtpProviders(): Promise<SmtpProvider[]> {
    const sqlite = new Database('data.db');
    const providers = sqlite.prepare('SELECT * FROM smtp_providers ORDER BY is_default DESC, name ASC').all();
    sqlite.close();
    return providers as SmtpProvider[];
  }

  async updateSmtpProvider(id: string, data: Partial<InsertSmtpProvider>): Promise<SmtpProvider> {
    const sqlite = new Database('data.db');

    const updates: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) { updates.push('name = ?'); values.push(data.name); }
    if (data.host !== undefined) { updates.push('host = ?'); values.push(data.host); }
    if (data.port !== undefined) { updates.push('port = ?'); values.push(data.port); }
    if (data.secure !== undefined) { updates.push('secure = ?'); values.push(data.secure ? 1 : 0); }
    if (data.authUser !== undefined) { updates.push('auth_user = ?'); values.push(data.authUser); }
    if (data.authPass !== undefined) { updates.push('auth_pass = ?'); values.push(data.authPass); }
    if (data.fromEmail !== undefined) { updates.push('from_email = ?'); values.push(data.fromEmail); }
    if (data.fromName !== undefined) { updates.push('from_name = ?'); values.push(data.fromName); }
    if (data.active !== undefined) { updates.push('active = ?'); values.push(data.active ? 1 : 0); }
    if (data.isDefault !== undefined) { updates.push('is_default = ?'); values.push(data.isDefault ? 1 : 0); }
    if (data.isBackup !== undefined) { updates.push('is_backup = ?'); values.push(data.isBackup ? 1 : 0); }
    if (data.updatedAt !== undefined) { updates.push('updated_at = ?'); values.push(data.updatedAt); }

    values.push(id);

    const sql = `UPDATE smtp_providers SET ${updates.join(', ')} WHERE id = ?`;
    const stmt = sqlite.prepare(sql);
    stmt.run(...values);

    const selectStmt = sqlite.prepare('SELECT * FROM smtp_providers WHERE id = ?');
    const provider = selectStmt.get(id);

    sqlite.close();
    return provider as SmtpProvider;
  }

  async deleteSmtpProvider(id: string): Promise<void> {
    const sqlite = new Database('data.db');
    const stmt = sqlite.prepare('DELETE FROM smtp_providers WHERE id = ?');
    stmt.run(id);
    sqlite.close();
  }

  // Custom Payment Link methods
  async createCustomPaymentLink(data: InsertCustomPaymentLink): Promise<CustomPaymentLink> {
    console.log('🔍 DATABASE STORAGE: createCustomPaymentLink called with data:', JSON.stringify(data, null, 2));

    const sqlite = new Database('data.db');

    const sql = `
      INSERT INTO custom_payment_links (
        id, name, payment_link, button_text, success_redirect_url,
        active, is_trial_link, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      data.id,
      data.name,
      data.paymentLink,
      data.buttonText,
      data.successRedirectUrl,
      data.active ? 1 : 0,
      data.isTrialLink ? 1 : 0,
      data.createdAt,
      data.updatedAt
    ];

    console.log('🔍 DATABASE STORAGE: SQL values:', JSON.stringify(values, null, 2));

    const stmt = sqlite.prepare(sql);
    const result = stmt.run(...values);

    const selectStmt = sqlite.prepare('SELECT * FROM custom_payment_links WHERE id = ?');
    const link = selectStmt.get(data.id);

    sqlite.close();
    return link as CustomPaymentLink;
  }

  async getCustomPaymentLinks(isTrialLink: boolean = false): Promise<CustomPaymentLink[]> {
    const sqlite = new Database('data.db');
    const links = sqlite.prepare('SELECT * FROM custom_payment_links WHERE is_trial_link = ? ORDER BY name ASC').all(isTrialLink ? 1 : 0);
    sqlite.close();
    return links as CustomPaymentLink[];
  }

  async updateCustomPaymentLink(id: string, data: Partial<InsertCustomPaymentLink>): Promise<CustomPaymentLink> {
    console.log('🔍 DATABASE UPDATE: updateCustomPaymentLink called with:', { id, data: JSON.stringify(data, null, 2) });

    const sqlite = new Database('data.db');

    const updates: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) { updates.push('name = ?'); values.push(data.name); }
    if (data.paymentLink !== undefined) { updates.push('payment_link = ?'); values.push(data.paymentLink); }
    if (data.buttonText !== undefined) { updates.push('button_text = ?'); values.push(data.buttonText); }
    if (data.successRedirectUrl !== undefined) { updates.push('success_redirect_url = ?'); values.push(data.successRedirectUrl); }
    if (data.active !== undefined) { updates.push('active = ?'); values.push(data.active ? 1 : 0); }
    if (data.isTrialLink !== undefined) { updates.push('is_trial_link = ?'); values.push(data.isTrialLink ? 1 : 0); }
    if (data.updatedAt !== undefined) { updates.push('updated_at = ?'); values.push(data.updatedAt); }

    values.push(id);

    const sql = `UPDATE custom_payment_links SET ${updates.join(', ')} WHERE id = ?`;
    console.log('🔍 DATABASE UPDATE: SQL query:', sql);
    console.log('🔍 DATABASE UPDATE: SQL values:', JSON.stringify(values, null, 2));

    const stmt = sqlite.prepare(sql);
    const result = stmt.run(...values);
    console.log('🔍 DATABASE UPDATE: SQL result:', result);

    const selectStmt = sqlite.prepare('SELECT * FROM custom_payment_links WHERE id = ?');
    const link = selectStmt.get(id);
    console.log('🔍 DATABASE UPDATE: Updated link:', JSON.stringify(link, null, 2));

    sqlite.close();
    return link as CustomPaymentLink;
  }

  async deleteCustomPaymentLink(id: string): Promise<void> {
    const sqlite = new Database('data.db');
    const stmt = sqlite.prepare('DELETE FROM custom_payment_links WHERE id = ?');
    stmt.run(id);
    sqlite.close();
  }

  // Payment Provider Configuration methods
  async updatePaymentProviderConfig(providerId: string, config: { active?: boolean; rotationMethod?: string }): Promise<void> {
    // For now, we'll store provider configuration in a simple way
    // In the future, this could be moved to a dedicated providers table
    console.log(`🔍 DATABASE: Updating payment provider config for ${providerId}:`, config);

    // Since we don't have a providers table yet, we'll just log this
    // The actual provider configuration is handled in the getPaymentConfig method
    // which returns hardcoded provider configurations with dynamic links
  }

  async deactivateOtherPaymentProviders(activeProviderId: string): Promise<void> {
    // For now, we'll just log this since provider activation/deactivation
    // is handled in the getPaymentConfig method
    console.log(`🔍 DATABASE: Deactivating other payment providers except ${activeProviderId}`);
  }

  // Email Template methods
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    try {
      // Use raw SQL to bypass Drizzle ORM issues
      const sqlite = new Database('data.db');
      const sql = `SELECT * FROM email_templates ORDER BY created_at DESC`;
      const templates = sqlite.prepare(sql).all();
      sqlite.close();

      return templates as EmailTemplate[];
    } catch (error) {
      console.error('Error fetching email templates:', error);
      return [];
    }
  }

  async getEmailTemplate(id: number): Promise<EmailTemplate | undefined> {
    try {
      // Use raw SQL to bypass Drizzle ORM issues
      const sqlite = new Database('data.db');
      const sql = `SELECT * FROM email_templates WHERE id = ?`;
      const template = sqlite.prepare(sql).get(id);
      sqlite.close();

      return template as EmailTemplate | undefined;
    } catch (error) {
      console.error('Error fetching email template:', error);
      return undefined;
    }
  }

  async createEmailTemplateRawSQL(template: InsertEmailTemplate): Promise<EmailTemplate> {
    try {
      console.log('🔍 RAW SQL NEW METHOD - Input template data:', JSON.stringify(template, null, 2));

      // Use raw SQL to completely bypass Drizzle ORM
      const sql = `
        INSERT INTO email_templates (
          template_id, name, description, subject, html_content,
          text_content, content, category, is_default, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      // Ensure all values are properly typed for SQLite
      const values = [
        template.templateId || null,
        template.name || null,
        template.description || null,
        template.subject || null,
        template.htmlContent || null,
        template.textContent || null,
        template.content || null,
        template.category || 'general',
        typeof template.isDefault === 'boolean' ? (template.isDefault ? 1 : 0) : (template.isDefault || 0),
        template.createdAt || new Date().toISOString(),
        template.updatedAt || new Date().toISOString()
      ];

      console.log('🔍 NEW METHOD - SQL values:', JSON.stringify(values, null, 2));

      // Create a direct SQLite connection
      const sqlite = new Database('data.db');

      // Execute raw SQL using SQLite directly
      const stmt = sqlite.prepare(sql);
      const result = stmt.run(...values);
      console.log('🔍 NEW METHOD - Raw SQL result:', result);

      // Get the inserted record
      const selectSql = `SELECT * FROM email_templates WHERE id = ?`;
      const selectStmt = sqlite.prepare(selectSql);
      const insertedRecord = selectStmt.get(result.lastInsertRowid);

      console.log('🔍 NEW METHOD - Inserted record:', insertedRecord);

      sqlite.close();

      return insertedRecord as EmailTemplate;
    } catch (error) {
      console.error('Error creating email template with raw SQL:', error);
      console.error('Error details:', error);
      throw error;
    }
  }

  async createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate> {
    // Delegate to the raw SQL method
    return this.createEmailTemplateRawSQL(template);
  }

  async updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined> {
    try {
      // Use raw SQL to bypass Drizzle ORM issues
      const sqlite = new Database('data.db');

      // Build dynamic SQL update query
      const updateFields = [];
      const values = [];

      if (update.templateId !== undefined) {
        updateFields.push('template_id = ?');
        values.push(update.templateId);
      }
      if (update.name !== undefined) {
        updateFields.push('name = ?');
        values.push(update.name);
      }
      if (update.description !== undefined) {
        updateFields.push('description = ?');
        values.push(update.description);
      }
      if (update.subject !== undefined) {
        updateFields.push('subject = ?');
        values.push(update.subject);
      }
      if (update.htmlContent !== undefined) {
        updateFields.push('html_content = ?');
        values.push(update.htmlContent);
      }
      if (update.textContent !== undefined) {
        updateFields.push('text_content = ?');
        values.push(update.textContent);
      }
      if (update.content !== undefined) {
        updateFields.push('content = ?');
        values.push(update.content);
      }
      if (update.category !== undefined) {
        updateFields.push('category = ?');
        values.push(update.category);
      }
      if (update.isDefault !== undefined) {
        updateFields.push('is_default = ?');
        values.push(typeof update.isDefault === 'boolean' ? (update.isDefault ? 1 : 0) : update.isDefault);
      }
      if (update.updatedAt !== undefined) {
        updateFields.push('updated_at = ?');
        values.push(update.updatedAt);
      }

      if (updateFields.length === 0) {
        sqlite.close();
        return undefined;
      }

      // Add the ID to the end of values array
      values.push(id);

      const sql = `UPDATE email_templates SET ${updateFields.join(', ')} WHERE id = ?`;
      const stmt = sqlite.prepare(sql);
      const result = stmt.run(...values);

      if (result.changes > 0) {
        // Get the updated record
        const selectSql = `SELECT * FROM email_templates WHERE id = ?`;
        const selectStmt = sqlite.prepare(selectSql);
        const updatedRecord = selectStmt.get(id);
        sqlite.close();
        return updatedRecord as EmailTemplate;
      }

      sqlite.close();
      return undefined;
    } catch (error) {
      console.error('Error updating email template:', error);
      return undefined;
    }
  }

  async deleteEmailTemplate(id: number): Promise<boolean> {
    try {
      // Use raw SQL to bypass Drizzle ORM issues
      const sqlite = new Database('data.db');
      const sql = `DELETE FROM email_templates WHERE id = ?`;
      const stmt = sqlite.prepare(sql);
      const result = stmt.run(id);
      sqlite.close();

      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting email template:', error);
      return false;
    }
  }

  async getPaypalButtons(): Promise<PaypalButton[]> {
    try {
      return await db.select().from(paypalButtons);
    } catch (error) {
      console.error('Error getting paypal buttons:', error);
      return [];
    }
  }

  async getPaypalButton(id: number): Promise<PaypalButton | undefined> {
    try {
      const result = await db.select().from(paypalButtons).where(eq(paypalButtons.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting paypal button:', error);
      return undefined;
    }
  }
  async createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton> {
    try {
      // Convert data for SQLite compatibility
      const sqliteData = {
        ...button,
        // Convert boolean to integer for SQLite
        active: button.active ? 1 : 0
      };

      const result = await db.insert(paypalButtons).values(sqliteData).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating paypal button:', error);
      throw error;
    }
  }
  async updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined> { return undefined; }
  async deletePaypalButton(id: number): Promise<boolean> { return false; }

  async getCustomInvoices(): Promise<CustomInvoice[]> { return []; }
  async getCustomInvoice(id: number): Promise<CustomInvoice | undefined> { return undefined; }
  async getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined> { return undefined; }
  async createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice> { throw new Error('Not implemented'); }
  async updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined> { return undefined; }
  async incrementCustomInvoiceViewCount(id: number): Promise<void> {}
  async markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined> { return undefined; }
  async deleteCustomInvoice(id: number): Promise<boolean> { return false; }

  // General Settings methods
  async getGeneralSettings(): Promise<GeneralSettingsRecord | undefined> {
    try {
      const result = await db.select().from(generalSettings).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting general settings:', error);
      return undefined;
    }
  }

  async createGeneralSettings(settings: InsertGeneralSettings): Promise<GeneralSettingsRecord> {
    try {
      // Convert data for SQLite compatibility
      const sqliteData = {
        ...settings,
        enableCheckout: settings.enableCheckout ? 1 : 0,
        enableCustomCheckout: settings.enableCustomCheckout ? 1 : 0,
        enableTestMode: settings.enableTestMode ? 1 : 0,
        defaultTestCustomerEnabled: settings.defaultTestCustomerEnabled ? 1 : 0,
        emailDomainRestrictionEnabled: settings.emailDomainRestrictionEnabled ? 1 : 0,
      };

      const result = await db.insert(generalSettings).values(sqliteData).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating general settings:', error);
      throw error;
    }
  }

  async updateGeneralSettings(update: Partial<InsertGeneralSettings>): Promise<GeneralSettingsRecord | undefined> {
    try {
      // Convert data for SQLite compatibility
      const sqliteUpdate: any = { ...update };

      // Convert booleans to integers for SQLite
      if ('enableCheckout' in update) {
        sqliteUpdate.enableCheckout = update.enableCheckout ? 1 : 0;
      }
      if ('enableCustomCheckout' in update) {
        sqliteUpdate.enableCustomCheckout = update.enableCustomCheckout ? 1 : 0;
      }
      if ('enableTestMode' in update) {
        sqliteUpdate.enableTestMode = update.enableTestMode ? 1 : 0;
      }
      if ('defaultTestCustomerEnabled' in update) {
        sqliteUpdate.defaultTestCustomerEnabled = update.defaultTestCustomerEnabled ? 1 : 0;
      }
      if ('emailDomainRestrictionEnabled' in update) {
        sqliteUpdate.emailDomainRestrictionEnabled = update.emailDomainRestrictionEnabled ? 1 : 0;
      }

      // Get the first (and only) record to update
      const existing = await this.getGeneralSettings();
      if (!existing) {
        // If no settings exist, create them
        return await this.createGeneralSettings({
          siteName: 'My Site',
          siteDescription: 'My Site Description',
          primaryColor: '#007bff',
          secondaryColor: '#6c757d',
          footerText: 'Copyright © 2024',
          enableCheckout: true,
          enableCustomCheckout: true,
          enableTestMode: false,
          defaultTestCustomerEnabled: false,
          emailDomainRestrictionEnabled: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          ...sqliteUpdate
        });
      }

      console.log('About to update database with sqliteUpdate:', sqliteUpdate);
      const result = await db.update(generalSettings).set(sqliteUpdate).where(eq(generalSettings.id, existing.id)).returning();
      console.log('Database update result:', result[0]);
      return result[0];
    } catch (error) {
      console.error('Error updating general settings:', error);
      return undefined;
    }
  }

  // Homepage Configuration methods
  async getHomepageConfig(): Promise<HomepageConfigRecord | undefined> {
    try {
      const result = await db.select().from(homepageConfig).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting homepage config:', error);
      return undefined;
    }
  }

  async createHomepageConfig(config: InsertHomepageConfig): Promise<HomepageConfigRecord> {
    try {
      const result = await db.insert(homepageConfig).values(config).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating homepage config:', error);
      throw error;
    }
  }

  async updateHomepageConfig(update: Partial<InsertHomepageConfig>): Promise<HomepageConfigRecord | undefined> {
    try {
      // Get the first (and only) record to update
      const existing = await this.getHomepageConfig();
      if (!existing) {
        // If no config exists, create it
        return await this.createHomepageConfig({
          sectionsData: JSON.stringify([]),
          seoSettings: JSON.stringify({}),
          themeSettings: JSON.stringify({}),
          version: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          ...update
        });
      }

      const result = await db.update(homepageConfig).set(update).where(eq(homepageConfig.id, existing.id)).returning();
      return result[0];
    } catch (error) {
      console.error('Error updating homepage config:', error);
      return undefined;
    }
  }

  // System Messages methods
  async getSystemMessages(): Promise<SystemMessageRecord[]> {
    try {
      return await db.select().from(systemMessages);
    } catch (error) {
      console.error('Error getting system messages:', error);
      return [];
    }
  }

  async getSystemMessage(id: number): Promise<SystemMessageRecord | undefined> {
    try {
      const result = await db.select().from(systemMessages).where(eq(systemMessages.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting system message:', error);
      return undefined;
    }
  }

  async getSystemMessageByMessageId(messageId: string): Promise<SystemMessageRecord | undefined> {
    try {
      const result = await db.select().from(systemMessages).where(eq(systemMessages.messageId, messageId)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting system message by messageId:', error);
      return undefined;
    }
  }

  async createSystemMessage(message: InsertSystemMessage): Promise<SystemMessageRecord> {
    try {
      // Convert data for SQLite compatibility
      const sqliteData = {
        ...message,
        isHtml: message.isHtml ? 1 : 0,
      };

      const result = await db.insert(systemMessages).values(sqliteData).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating system message:', error);
      throw error;
    }
  }

  async updateSystemMessage(id: number, update: Partial<InsertSystemMessage>): Promise<SystemMessageRecord | undefined> {
    try {
      // Convert data for SQLite compatibility
      const sqliteUpdate: any = { ...update };

      // Convert booleans to integers for SQLite
      if ('isHtml' in update) {
        sqliteUpdate.isHtml = update.isHtml ? 1 : 0;
      }

      const result = await db.update(systemMessages).set(sqliteUpdate).where(eq(systemMessages.id, id)).returning();
      return result[0];
    } catch (error) {
      console.error('Error updating system message:', error);
      return undefined;
    }
  }

  async deleteSystemMessage(id: number): Promise<boolean> {
    try {
      const result = await db.delete(systemMessages).where(eq(systemMessages.id, id));
      return result.changes ? result.changes > 0 : true;
    } catch (error) {
      console.error('Error deleting system message:', error);
      return false;
    }
  }

  // Contact and Embed Code methods
  async createContactInquiry?(inquiry: any): Promise<any> { return inquiry; }
  async getContactInquiries?(): Promise<any[]> { return []; }
  async updateContactInquiry?(id: number, update: any): Promise<any> { return update; }

  async getEmbedCodes(): Promise<EmbedCode[]> { return []; }
  async getEmbedCode(id: string): Promise<EmbedCode | undefined> { return undefined; }
  async createEmbedCode(embedCode: EmbedCode): Promise<EmbedCode> { return embedCode; }
  async updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined> { return undefined; }
  async deleteEmbedCode(id: string): Promise<boolean> { return false; }
}
