import React from 'react';
import ProductCard from './ProductCard';
import { ProductGridProps } from '@/lib/types';
import { Skeleton } from '@/components/ui/skeleton';

// Loading skeleton for products
const ProductSkeleton = () => (
  <div className="bg-card rounded-lg overflow-hidden shadow-sm border">
    <Skeleton className="w-full h-48" />
    <div className="p-4">
      <Skeleton className="h-6 w-3/4 mb-2" />
      <Skeleton className="h-4 w-full mb-1" />
      <Skeleton className="h-4 w-5/6 mb-3" />
      <div className="flex justify-between items-center">
        <Skeleton className="h-6 w-16" />
        <Skeleton className="h-10 w-20 rounded-md" />
      </div>
    </div>
  </div>
);

const ProductGrid: React.FC<ProductGridProps> = ({ products, onSelectProduct, isLoading }) => {
  return (
    <div className="container mx-auto">
      <h1 className="text-3xl font-bold mb-8">Digital Products</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          // Show 6 skeleton cards while loading
          Array(6).fill(0).map((_, index) => (
            <ProductSkeleton key={index} />
          ))
        ) : (
          products.map((product) => (
            <ProductCard 
              key={product.id} 
              product={product}
              onSelect={onSelectProduct}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default ProductGrid;
