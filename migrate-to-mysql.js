#!/usr/bin/env node

/**
 * SQLite to MySQL Migration Script
 * 
 * This script migrates data from your local SQLite database to MySQL on CloudPanel.io
 * 
 * Usage:
 * 1. Install required dependencies: npm install mysql2 better-sqlite3
 * 2. Update the MySQL connection details below
 * 3. Run: node migrate-to-mysql.js
 */

const Database = require('better-sqlite3');
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Configuration
const SQLITE_DB_PATH = './data.db';
const MYSQL_CONFIG = {
  host: 'localhost',
  user: 'your-mysql-username',
  password: 'your-mysql-password',
  database: 'your-mysql-database',
  port: 3306
};

// Tables to migrate (in order to handle foreign key constraints)
const TABLES_ORDER = [
  'users',
  'devices',
  'recovery_codes',
  'products',
  'invoices',
  'custom_checkout_pages',
  'allowed_emails',
  'email_templates',
  'paypal_buttons',
  'custom_invoices',
  'smtp_providers',
  'custom_payment_links',
  'general_settings',
  'homepage_config',
  'system_messages'
];

// Data type mapping from SQLite to MySQL
const TYPE_MAPPING = {
  'INTEGER': 'INT',
  'TEXT': 'TEXT',
  'REAL': 'DECIMAL(10,2)',
  'BLOB': 'BLOB'
};

async function migrateSQLiteToMySQL() {
  let sqliteDb;
  let mysqlDb;

  try {
    console.log('🚀 Starting SQLite to MySQL migration...');

    // Check if SQLite database exists
    if (!fs.existsSync(SQLITE_DB_PATH)) {
      throw new Error(`SQLite database not found at: ${SQLITE_DB_PATH}`);
    }

    // Connect to SQLite
    console.log('📂 Connecting to SQLite database...');
    sqliteDb = new Database(SQLITE_DB_PATH, { readonly: true });

    // Connect to MySQL
    console.log('🔗 Connecting to MySQL database...');
    mysqlDb = await mysql.createConnection(MYSQL_CONFIG);

    // Test MySQL connection
    await mysqlDb.execute('SELECT 1');
    console.log('✅ MySQL connection successful');

    // Get all tables from SQLite
    const tables = sqliteDb.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `).all();

    console.log(`📋 Found ${tables.length} tables to migrate:`, tables.map(t => t.name));

    // Disable foreign key checks temporarily
    await mysqlDb.execute('SET FOREIGN_KEY_CHECKS = 0');

    let totalRecords = 0;

    // Migrate each table
    for (const table of tables) {
      const tableName = table.name;
      console.log(`\n📦 Migrating table: ${tableName}`);

      try {
        // Get table schema from SQLite
        const schema = sqliteDb.prepare(`PRAGMA table_info(${tableName})`).all();
        
        // Get all data from SQLite table
        const rows = sqliteDb.prepare(`SELECT * FROM ${tableName}`).all();
        
        if (rows.length === 0) {
          console.log(`   ⚠️  Table ${tableName} is empty, skipping...`);
          continue;
        }

        console.log(`   📊 Found ${rows.length} records`);

        // Clear existing data in MySQL table (optional - comment out if you want to preserve existing data)
        await mysqlDb.execute(`DELETE FROM ${tableName}`);
        console.log(`   🧹 Cleared existing data in MySQL table`);

        // Get column names
        const columns = Object.keys(rows[0]);
        const placeholders = columns.map(() => '?').join(', ');

        // Prepare insert statement
        const insertSQL = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;

        // Insert data in batches
        const batchSize = 100;
        for (let i = 0; i < rows.length; i += batchSize) {
          const batch = rows.slice(i, i + batchSize);
          
          for (const row of batch) {
            const values = columns.map(col => {
              let value = row[col];
              
              // Handle special data types
              if (value === null) return null;
              if (typeof value === 'boolean') return value ? 1 : 0;
              if (Buffer.isBuffer(value)) return value;
              
              return value;
            });

            try {
              await mysqlDb.execute(insertSQL, values);
            } catch (error) {
              console.error(`   ❌ Error inserting row in ${tableName}:`, error.message);
              console.error(`   📄 Row data:`, row);
              // Continue with next row instead of failing completely
            }
          }
        }

        totalRecords += rows.length;
        console.log(`   ✅ Successfully migrated ${rows.length} records`);

      } catch (error) {
        console.error(`   ❌ Error migrating table ${tableName}:`, error.message);
        // Continue with next table
      }
    }

    // Re-enable foreign key checks
    await mysqlDb.execute('SET FOREIGN_KEY_CHECKS = 1');

    console.log(`\n🎉 Migration completed successfully!`);
    console.log(`📊 Total records migrated: ${totalRecords}`);

    // Verify migration
    console.log('\n🔍 Verifying migration...');
    for (const table of tables) {
      try {
        const [rows] = await mysqlDb.execute(`SELECT COUNT(*) as count FROM ${table.name}`);
        const mysqlCount = rows[0].count;
        const sqliteCount = sqliteDb.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get().count;
        
        if (mysqlCount === sqliteCount) {
          console.log(`   ✅ ${table.name}: ${mysqlCount} records (matches SQLite)`);
        } else {
          console.log(`   ⚠️  ${table.name}: MySQL=${mysqlCount}, SQLite=${sqliteCount} (mismatch!)`);
        }
      } catch (error) {
        console.log(`   ❌ ${table.name}: Error verifying - ${error.message}`);
      }
    }

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    // Close connections
    if (sqliteDb) {
      sqliteDb.close();
      console.log('\n📂 SQLite connection closed');
    }
    if (mysqlDb) {
      await mysqlDb.end();
      console.log('🔗 MySQL connection closed');
    }
  }
}

// Backup function
async function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = `./backup-before-migration-${timestamp}.db`;
  
  try {
    fs.copyFileSync(SQLITE_DB_PATH, backupPath);
    console.log(`💾 Backup created: ${backupPath}`);
    return backupPath;
  } catch (error) {
    console.error('❌ Failed to create backup:', error.message);
    throw error;
  }
}

// Export data to SQL file (alternative method)
async function exportToSQL() {
  const sqliteDb = new Database(SQLITE_DB_PATH, { readonly: true });
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const exportPath = `./export-${timestamp}.sql`;
  
  try {
    console.log('📤 Exporting SQLite data to SQL file...');
    
    const tables = sqliteDb.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
    `).all();

    let sqlContent = '-- SQLite to MySQL Export\n';
    sqlContent += `-- Generated on: ${new Date().toISOString()}\n\n`;
    sqlContent += 'SET FOREIGN_KEY_CHECKS = 0;\n\n';

    for (const table of tables) {
      const rows = sqliteDb.prepare(`SELECT * FROM ${table.name}`).all();
      
      if (rows.length === 0) continue;

      sqlContent += `-- Table: ${table.name}\n`;
      sqlContent += `DELETE FROM ${table.name};\n`;

      for (const row of rows) {
        const columns = Object.keys(row);
        const values = columns.map(col => {
          const value = row[col];
          if (value === null) return 'NULL';
          if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
          if (typeof value === 'boolean') return value ? '1' : '0';
          return value;
        });

        sqlContent += `INSERT INTO ${table.name} (${columns.join(', ')}) VALUES (${values.join(', ')});\n`;
      }
      
      sqlContent += '\n';
    }

    sqlContent += 'SET FOREIGN_KEY_CHECKS = 1;\n';

    fs.writeFileSync(exportPath, sqlContent);
    console.log(`✅ SQL export created: ${exportPath}`);
    console.log('📋 You can import this file to MySQL using:');
    console.log(`   mysql -u username -p database_name < ${exportPath}`);
    
    return exportPath;
  } catch (error) {
    console.error('❌ Export failed:', error.message);
    throw error;
  } finally {
    sqliteDb.close();
  }
}

// Main execution
async function main() {
  console.log('🔄 SQLite to MySQL Migration Tool\n');

  // Check if required modules are installed
  try {
    require('mysql2');
    require('better-sqlite3');
  } catch (error) {
    console.error('❌ Required dependencies not found. Please install them:');
    console.error('   npm install mysql2 better-sqlite3');
    process.exit(1);
  }

  // Show configuration
  console.log('📋 Configuration:');
  console.log(`   SQLite DB: ${SQLITE_DB_PATH}`);
  console.log(`   MySQL Host: ${MYSQL_CONFIG.host}:${MYSQL_CONFIG.port}`);
  console.log(`   MySQL Database: ${MYSQL_CONFIG.database}`);
  console.log(`   MySQL User: ${MYSQL_CONFIG.user}\n`);

  // Create backup
  await createBackup();

  // Ask user for confirmation
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const answer = await new Promise(resolve => {
    readline.question('Do you want to proceed with the migration? (y/N): ', resolve);
  });
  
  readline.close();

  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    console.log('❌ Migration cancelled by user');
    process.exit(0);
  }

  // Perform migration
  await migrateSQLiteToMySQL();

  // Also create SQL export as backup
  await exportToSQL();

  console.log('\n🎉 All done! Your data has been migrated to MySQL.');
  console.log('💡 Don\'t forget to update your DATABASE_URL in the .env file:');
  console.log(`   DATABASE_URL=mysql://${MYSQL_CONFIG.user}:${MYSQL_CONFIG.password}@${MYSQL_CONFIG.host}:${MYSQL_CONFIG.port}/${MYSQL_CONFIG.database}`);
}

// Run the migration
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { migrateSQLiteToMySQL, createBackup, exportToSQL };
