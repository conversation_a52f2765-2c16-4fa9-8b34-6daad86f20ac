import { Router, Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { storage } from '../storage-factory';

export const homepageRouter = Router();

// Session augmentation for TypeScript
declare module 'express-session' {
  interface SessionData {
    isAdmin: boolean;
    username: string;
    userId: number;
    rememberMe: boolean;
    requiresTwoFactor: boolean;
    twoFactorVerified: boolean;
  }
}

// Admin authentication middleware
const isAdmin = (req: Request, res: Response, next: NextFunction) => {
  console.log('Checking admin session for homepage:', req.session);

  if (req.session.isAdmin) {
    if (req.session.requiresTwoFactor && !req.session.twoFactorVerified) {
      console.log('2FA required but not verified');
      return res.status(401).json({
        message: 'Two-factor authentication required',
        requiresTwoFactor: true
      });
    }

    console.log('Admin session verified for homepage:', true);
    next();
  } else {
    console.log('Admin session verified for homepage:', false);
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Validation schemas
const seoSettingsSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  keywords: z.string().optional().default(""),
  ogTitle: z.string().optional().default(""),
  ogDescription: z.string().optional().default(""),
  ogImage: z.string().optional().default(""),
  twitterTitle: z.string().optional().default(""),
  twitterDescription: z.string().optional().default(""),
  twitterImage: z.string().optional().default("")
});

const themeSettingsSchema = z.object({
  primaryColor: z.string().min(1, "Primary color is required"),
  secondaryColor: z.string().min(1, "Secondary color is required"),
  accentColor: z.string().optional().default("#009cde"),
  backgroundColor: z.string().optional().default("#ffffff"),
  textColor: z.string().optional().default("#1e293b"),
  fontFamily: z.string().optional().default("Inter, system-ui, sans-serif"),
  borderRadius: z.string().optional().default("8px"),
  spacing: z.string().optional().default("1rem")
});

const heroSectionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  subtitle: z.string().optional().default(""),
  description: z.string().optional().default(""),
  ctaText: z.string().optional().default(""),
  ctaLink: z.string().optional().default(""),
  backgroundImage: z.string().optional().default(""),
  backgroundType: z.enum(['image', 'gradient', 'solid']).default('gradient'),
  backgroundColor: z.string().optional().default(""),
  textColor: z.string().optional().default("#ffffff"),
  showVideo: z.boolean().default(false),
  videoUrl: z.string().optional().default("")
});

const featureItemSchema = z.object({
  id: z.string(),
  icon: z.string().optional().default("⭐"),
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  enabled: z.boolean().default(true)
});

const featuresSectionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  subtitle: z.string().optional().default(""),
  features: z.array(featureItemSchema),
  layout: z.enum(['grid', 'list', 'carousel']).default('grid'),
  columns: z.number().min(1).max(6).default(3)
});

const productsSectionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  subtitle: z.string().optional().default(""),
  showAllProducts: z.boolean().default(true),
  featuredProductIds: z.array(z.number()).default([]),
  layout: z.enum(['grid', 'carousel']).default('grid'),
  columns: z.number().min(1).max(6).default(3),
  showPrices: z.boolean().default(true),
  showDescriptions: z.boolean().default(true)
});

const ctaSectionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional().default(""),
  primaryButtonText: z.string().optional().default(""),
  primaryButtonLink: z.string().optional().default(""),
  secondaryButtonText: z.string().optional().default(""),
  secondaryButtonLink: z.string().optional().default(""),
  backgroundType: z.enum(['image', 'gradient', 'solid']).default('solid'),
  backgroundColor: z.string().optional().default("#f8fafc"),
  backgroundImage: z.string().optional().default(""),
  textColor: z.string().optional().default("#1e293b")
});

const sectionSchema = z.object({
  id: z.string(),
  type: z.enum(['hero', 'features', 'testimonials', 'products', 'faq', 'cta']),
  title: z.string().min(1, "Title is required"),
  enabled: z.boolean().default(true),
  order: z.number().min(1),
  content: z.union([
    heroSectionSchema,
    featuresSectionSchema,
    productsSectionSchema,
    ctaSectionSchema
  ])
});

// Get homepage configuration (public endpoint)
homepageRouter.get('/', async (req: Request, res: Response) => {
  try {
    console.log('GET /api/homepage - Fetching homepage configuration');
    const dbConfig = await storage.getHomepageConfig();

    if (!dbConfig) {
      // Return default configuration if none exists
      const defaultConfig = {
        sections: [],
        seo: {
          title: 'My Website',
          description: 'Welcome to my website',
          keywords: '',
          ogTitle: '',
          ogDescription: '',
          ogImage: '',
          twitterTitle: '',
          twitterDescription: '',
          twitterImage: ''
        },
        theme: {
          primaryColor: '#6366f1',
          secondaryColor: '#4f46e5',
          accentColor: '#8b5cf6',
          backgroundColor: '#ffffff',
          textColor: '#1e293b',
          fontFamily: 'Inter, system-ui, sans-serif',
          borderRadius: '8px',
          spacing: '1rem'
        },
        lastUpdated: new Date().toISOString(),
        version: 1
      };
      return res.json(defaultConfig);
    }

    // Convert database format to API format
    const config = {
      sections: JSON.parse(dbConfig.sectionsData),
      seo: JSON.parse(dbConfig.seoSettings),
      theme: JSON.parse(dbConfig.themeSettings),
      lastUpdated: dbConfig.updatedAt,
      version: dbConfig.version
    };

    console.log('Homepage configuration fetched successfully:', {
      sectionsCount: config.sections.length,
      version: config.version,
      lastUpdated: config.lastUpdated
    });
    res.json(config);
  } catch (error) {
    console.error('Error fetching homepage configuration:', error);
    res.status(500).json({ message: 'Failed to fetch homepage configuration' });
  }
});

// Update entire homepage configuration (admin only)
homepageRouter.put('/', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('PUT /api/homepage - Updating homepage configuration');
    console.log('Request body:', JSON.stringify(req.body, null, 2));

    const { sections, seo, theme } = req.body;

    // Get current config to increment version
    const currentConfig = await storage.getHomepageConfig();
    const currentVersion = currentConfig ? currentConfig.version : 0;

    const dbUpdate = {
      sectionsData: JSON.stringify(sections || []),
      seoSettings: JSON.stringify(seo || {}),
      themeSettings: JSON.stringify(theme || {}),
      version: currentVersion + 1,
      updatedAt: new Date().toISOString()
    };

    const updatedDbConfig = await storage.updateHomepageConfig(dbUpdate);

    // Convert back to API format
    const updatedConfig = {
      sections: JSON.parse(updatedDbConfig.sectionsData),
      seo: JSON.parse(updatedDbConfig.seoSettings),
      theme: JSON.parse(updatedDbConfig.themeSettings),
      lastUpdated: updatedDbConfig.updatedAt,
      version: updatedDbConfig.version
    };

    console.log('Homepage configuration updated successfully:', {
      sectionsCount: updatedConfig.sections.length,
      version: updatedConfig.version,
      lastUpdated: updatedConfig.lastUpdated
    });

    res.json(updatedConfig);
  } catch (error) {
    console.error('Error updating homepage configuration:', error);
    res.status(500).json({ message: 'Failed to update homepage configuration' });
  }
});

// Update SEO settings (admin only)
homepageRouter.put('/seo', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('PUT /api/homepage/seo - Updating SEO settings');
    console.log('SEO data:', JSON.stringify(req.body, null, 2));

    const validatedSEO = seoSettingsSchema.parse(req.body);

    // Get current config
    const currentConfig = await storage.getHomepageConfig();
    if (!currentConfig) {
      return res.status(404).json({ message: 'Homepage configuration not found' });
    }

    const dbUpdate = {
      seoSettings: JSON.stringify(validatedSEO),
      version: currentConfig.version + 1,
      updatedAt: new Date().toISOString()
    };

    const updatedDbConfig = await storage.updateHomepageConfig(dbUpdate);

    // Convert back to API format
    const updatedConfig = {
      sections: JSON.parse(updatedDbConfig.sectionsData),
      seo: JSON.parse(updatedDbConfig.seoSettings),
      theme: JSON.parse(updatedDbConfig.themeSettings),
      lastUpdated: updatedDbConfig.updatedAt,
      version: updatedDbConfig.version
    };

    console.log('SEO settings updated successfully');
    res.json(updatedConfig);
  } catch (error) {
    console.error('Error updating SEO settings:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update SEO settings' });
  }
});

// Update theme settings (admin only)
homepageRouter.put('/theme', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('PUT /api/homepage/theme - Updating theme settings');
    console.log('Theme data:', JSON.stringify(req.body, null, 2));

    const validatedTheme = themeSettingsSchema.parse(req.body);

    // Get current config
    const currentConfig = await storage.getHomepageConfig();
    if (!currentConfig) {
      return res.status(404).json({ message: 'Homepage configuration not found' });
    }

    const dbUpdate = {
      themeSettings: JSON.stringify(validatedTheme),
      version: currentConfig.version + 1,
      updatedAt: new Date().toISOString()
    };

    const updatedDbConfig = await storage.updateHomepageConfig(dbUpdate);

    // Convert back to API format
    const updatedConfig = {
      sections: JSON.parse(updatedDbConfig.sectionsData),
      seo: JSON.parse(updatedDbConfig.seoSettings),
      theme: JSON.parse(updatedDbConfig.themeSettings),
      lastUpdated: updatedDbConfig.updatedAt,
      version: updatedDbConfig.version
    };

    console.log('Theme settings updated successfully');
    res.json(updatedConfig);
  } catch (error) {
    console.error('Error updating theme settings:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update theme settings' });
  }
});

// Update specific section (admin only)
homepageRouter.put('/sections/:sectionId', isAdmin, async (req: Request, res: Response) => {
  try {
    const { sectionId } = req.params;
    console.log(`PUT /api/homepage/sections/${sectionId} - Updating section`);
    console.log('Section data:', JSON.stringify(req.body, null, 2));

    const validatedSection = sectionSchema.parse(req.body);
    const updatedConfig = updateSection(sectionId, validatedSection);

    console.log(`Section ${sectionId} updated successfully`);
    res.json(updatedConfig);
  } catch (error) {
    console.error('Error updating section:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    
    res.status(500).json({ message: 'Failed to update section' });
  }
});

// Add new section (admin only)
homepageRouter.post('/sections', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('POST /api/homepage/sections - Adding new section');
    console.log('New section data:', JSON.stringify(req.body, null, 2));

    const validatedSection = sectionSchema.parse(req.body);
    const updatedConfig = addSection(validatedSection);

    console.log(`New section ${validatedSection.id} added successfully`);
    res.status(201).json(updatedConfig);
  } catch (error) {
    console.error('Error adding section:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    
    res.status(500).json({ message: 'Failed to add section' });
  }
});

// Delete section (admin only)
homepageRouter.delete('/sections/:sectionId', isAdmin, async (req: Request, res: Response) => {
  try {
    const { sectionId } = req.params;
    console.log(`DELETE /api/homepage/sections/${sectionId} - Removing section`);

    const updatedConfig = removeSection(sectionId);

    console.log(`Section ${sectionId} removed successfully`);
    res.json(updatedConfig);
  } catch (error) {
    console.error('Error removing section:', error);
    res.status(500).json({ message: 'Failed to remove section' });
  }
});

// Reorder sections (admin only)
homepageRouter.put('/sections/reorder', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('PUT /api/homepage/sections/reorder - Reordering sections');
    const { sectionIds } = req.body;
    console.log('New section order:', sectionIds);

    if (!Array.isArray(sectionIds)) {
      return res.status(400).json({ message: 'sectionIds must be an array' });
    }

    const updatedConfig = reorderSections(sectionIds);

    console.log('Sections reordered successfully');
    res.json(updatedConfig);
  } catch (error) {
    console.error('Error reordering sections:', error);
    res.status(500).json({ message: 'Failed to reorder sections' });
  }
});

// Reset to default configuration (admin only)
homepageRouter.post('/reset', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('POST /api/homepage/reset - Resetting to default configuration');

    const defaultConfig = resetHomepageConfig();

    console.log('Homepage configuration reset to default successfully');
    res.json(defaultConfig);
  } catch (error) {
    console.error('Error resetting homepage configuration:', error);
    res.status(500).json({ message: 'Failed to reset homepage configuration' });
  }
});
