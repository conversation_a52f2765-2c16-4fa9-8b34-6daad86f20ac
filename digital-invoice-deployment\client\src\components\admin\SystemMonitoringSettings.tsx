import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle2, 
  Clock, 
  Cpu, 
  HardDrive, 
  MemoryStick, 
  Settings,
  TrendingUp,
  Zap
} from 'lucide-react';
import { apiRequest } from '@/lib/api';

interface SystemMetrics {
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  uptime: number;
  timestamp: string;
}

interface MonitoringConfig {
  enabled: boolean;
  intervalMinutes: number;
  alertThresholds: {
    cpu: number;
    memory: number;
    disk: number;
    responseTime: number;
  };
}

const SystemMonitoringSettings: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch current monitoring status
  const { data: monitoringStatus, isLoading: statusLoading } = useQuery({
    queryKey: ['/api/admin/system-monitor/status'],
    queryFn: () => apiRequest('/api/admin/system-monitor/status', 'GET'),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Fetch current system metrics
  const { data: systemMetrics, isLoading: metricsLoading } = useQuery({
    queryKey: ['/api/admin/system-monitor/metrics'],
    queryFn: () => apiRequest('/api/admin/system-monitor/metrics', 'GET'),
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  // Fetch monitoring configuration
  const { data: config, isLoading: configLoading } = useQuery({
    queryKey: ['/api/admin/system-monitor/config'],
    queryFn: () => apiRequest('/api/admin/system-monitor/config', 'GET'),
  });

  // Toggle monitoring mutation
  const toggleMonitoringMutation = useMutation({
    mutationFn: (enabled: boolean) => 
      apiRequest('/api/admin/system-monitor/toggle', 'POST', { enabled }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/system-monitor/status'] });
      toast({
        title: 'Success',
        description: 'System monitoring settings updated',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update monitoring: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Update configuration mutation
  const updateConfigMutation = useMutation({
    mutationFn: (newConfig: MonitoringConfig) => 
      apiRequest('/api/admin/system-monitor/config', 'PUT', newConfig),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/system-monitor/config'] });
      toast({
        title: 'Success',
        description: 'Monitoring configuration updated',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update configuration: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Send test alert mutation
  const sendTestAlertMutation = useMutation({
    mutationFn: () => apiRequest('/api/admin/system-monitor/test-alert', 'POST'),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Test alert sent to Telegram',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to send test alert: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  const formatBytes = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const getStatusColor = (percentage: number, threshold: number) => {
    if (percentage >= threshold) return 'text-red-600';
    if (percentage >= threshold * 0.8) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getStatusBadge = (percentage: number, threshold: number) => {
    if (percentage >= threshold) return <Badge variant="destructive">Critical</Badge>;
    if (percentage >= threshold * 0.8) return <Badge variant="secondary">Warning</Badge>;
    return <Badge variant="default" className="bg-green-600">Healthy</Badge>;
  };

  if (statusLoading || metricsLoading || configLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Activity className="h-8 w-8" />
          <h2 className="text-xl font-bold">System Monitoring</h2>
        </div>
        <div className="text-center py-8">Loading system monitoring data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Activity className="h-8 w-8" />
          <h2 className="text-xl font-bold">System Monitoring</h2>
        </div>
        <div className="flex items-center space-x-2">
          {monitoringStatus?.isActive ? (
            <Badge variant="default" className="bg-green-600">
              <CheckCircle2 className="h-3 w-3 mr-1" />
              Active
            </Badge>
          ) : (
            <Badge variant="secondary">
              <Clock className="h-3 w-3 mr-1" />
              Inactive
            </Badge>
          )}
        </div>
      </div>

      {/* Current System Status */}
      {systemMetrics && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Current System Status</span>
            </CardTitle>
            <CardDescription>
              Real-time system performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* CPU Usage */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Cpu className="h-4 w-4" />
                    <span className="font-medium">CPU Usage</span>
                  </div>
                  {getStatusBadge(systemMetrics.cpu.usage, config?.alertThresholds?.cpu || 80)}
                </div>
                <div className={`text-2xl font-bold ${getStatusColor(systemMetrics.cpu.usage, config?.alertThresholds?.cpu || 80)}`}>
                  {systemMetrics.cpu.usage.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  Load: {systemMetrics.cpu.loadAverage.map(l => l.toFixed(2)).join(', ')}
                </div>
              </div>

              {/* Memory Usage */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <MemoryStick className="h-4 w-4" />
                    <span className="font-medium">Memory Usage</span>
                  </div>
                  {getStatusBadge(systemMetrics.memory.percentage, config?.alertThresholds?.memory || 85)}
                </div>
                <div className={`text-2xl font-bold ${getStatusColor(systemMetrics.memory.percentage, config?.alertThresholds?.memory || 85)}`}>
                  {systemMetrics.memory.percentage.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  {formatBytes(systemMetrics.memory.used)} / {formatBytes(systemMetrics.memory.total)}
                </div>
              </div>

              {/* Disk Usage */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <HardDrive className="h-4 w-4" />
                    <span className="font-medium">Disk Usage</span>
                  </div>
                  {getStatusBadge(systemMetrics.disk.percentage, config?.alertThresholds?.disk || 90)}
                </div>
                <div className={`text-2xl font-bold ${getStatusColor(systemMetrics.disk.percentage, config?.alertThresholds?.disk || 90)}`}>
                  {systemMetrics.disk.percentage.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  {formatBytes(systemMetrics.disk.used)} / {formatBytes(systemMetrics.disk.total)}
                </div>
              </div>
            </div>

            <Separator className="my-4" />

            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>System Uptime: {formatUptime(systemMetrics.uptime)}</span>
              <span>Last Updated: {new Date(systemMetrics.timestamp).toLocaleString()}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Monitoring Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Monitoring Controls</span>
          </CardTitle>
          <CardDescription>
            Configure system monitoring and alerts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable/Disable Monitoring */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-base font-medium">Enable System Monitoring</Label>
              <p className="text-sm text-muted-foreground">
                Monitor system performance and send alerts via Telegram
              </p>
            </div>
            <Switch
              checked={monitoringStatus?.isActive || false}
              onCheckedChange={(checked) => toggleMonitoringMutation.mutate(checked)}
              disabled={toggleMonitoringMutation.isPending}
            />
          </div>

          <Separator />

          {/* Alert Thresholds */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Alert Thresholds</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="cpu-threshold">CPU Usage (%)</Label>
                <Input
                  id="cpu-threshold"
                  type="number"
                  min="1"
                  max="100"
                  defaultValue={config?.alertThresholds?.cpu || 80}
                  placeholder="80"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="memory-threshold">Memory Usage (%)</Label>
                <Input
                  id="memory-threshold"
                  type="number"
                  min="1"
                  max="100"
                  defaultValue={config?.alertThresholds?.memory || 85}
                  placeholder="85"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="disk-threshold">Disk Usage (%)</Label>
                <Input
                  id="disk-threshold"
                  type="number"
                  min="1"
                  max="100"
                  defaultValue={config?.alertThresholds?.disk || 90}
                  placeholder="90"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="interval">Monitoring Interval (minutes)</Label>
                <Input
                  id="interval"
                  type="number"
                  min="1"
                  max="60"
                  defaultValue={config?.intervalMinutes || 5}
                  placeholder="5"
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex space-x-4">
            <Button
              onClick={() => sendTestAlertMutation.mutate()}
              disabled={sendTestAlertMutation.isPending}
              variant="outline"
            >
              <Zap className="h-4 w-4 mr-2" />
              Send Test Alert
            </Button>
            <Button
              onClick={() => queryClient.invalidateQueries({ queryKey: ['/api/admin/system-monitor'] })}
              variant="outline"
            >
              <Activity className="h-4 w-4 mr-2" />
              Refresh Data
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Telegram Commands</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><code>/system</code> - Get current system performance report</p>
            <p><code>/monitor</code> - Toggle system monitoring on/off</p>
            <p><code>/errors</code> - View recent error logs</p>
            <p className="text-muted-foreground mt-4">
              Make sure your Telegram bot is configured in the Telegram Settings to receive system alerts.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemMonitoringSettings;
