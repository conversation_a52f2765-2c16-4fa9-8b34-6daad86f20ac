import * as React from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getSystemMessages, updateSystemMessage } from "@/api/systemMessages";
import { SystemMessage } from "@/lib/system-messages";

type SystemMessagesContextType = {
  messages: SystemMessage[];
  isLoading: boolean;
  error: Error | null;
  getMessage: (id: string) => SystemMessage | undefined;
  getMessagesByCategory: (category: string) => SystemMessage[];
  updateMessage: (id: string, data: Partial<SystemMessage>) => Promise<void>;
};

const SystemMessagesContext = React.createContext<SystemMessagesContextType | undefined>(undefined);

export function SystemMessagesProvider({ children }: { children: React.ReactNode }) {
  const queryClient = useQueryClient();

  const { data: messages = [], isLoading, error } = useQuery({
    queryKey: ["systemMessages"],
    queryFn: getSystemMessages,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const getMessage = React.useCallback(
    (id: string) => {
      return messages.find((msg) => msg.id === id);
    },
    [messages]
  );

  const getMessagesByCategory = React.useCallback(
    (category: string) => {
      return messages.filter((msg) => msg.category === category);
    },
    [messages]
  );

  const updateMessage = React.useCallback(
    async (id: string, data: Partial<SystemMessage>) => {
      await updateSystemMessage(id, data);
      queryClient.invalidateQueries({ queryKey: ["systemMessages"] });
    },
    [queryClient]
  );

  const value = React.useMemo(
    () => ({
      messages,
      isLoading,
      error,
      getMessage,
      getMessagesByCategory,
      updateMessage,
    }),
    [messages, isLoading, error, getMessage, getMessagesByCategory, updateMessage]
  );

  return (
    <SystemMessagesContext.Provider value={value}>
      {children}
    </SystemMessagesContext.Provider>
  );
}

export function useSystemMessages() {
  const context = React.useContext(SystemMessagesContext);
  if (context === undefined) {
    throw new Error("useSystemMessages must be used within a SystemMessagesProvider");
  }
  return context;
}

// Helper function to render message content with HTML support
export function renderMessageContent(message?: SystemMessage, fallback: string = ""): React.ReactNode {
  if (!message) return fallback;

  if (message.isHtml) {
    return <div dangerouslySetInnerHTML={{ __html: message.content }} />;
  }

  return message.content;
}
