import { Request, Response, NextFunction } from 'express';

// Middleware to check if user is authenticated as admin
export function isAdmin(req: Request, res: Response, next: NextFunction) {
  // Check if session exists and user is admin
  if (req.session && req.session.isAdmin) {
    // Log session info for debugging
    console.log('Checking session:', {
      id: req.session.id,
      isAdmin: req.session.isAdmin
    });

    // User is authenticated as admin, proceed
    next();
  } else {
    // User is not authenticated as admin
    res.status(401).json({ message: 'Unauthorized' });
  }
}

// Middleware to check for admin access token
export function checkAdminAccessToken(req: Request, res: Response, next: NextFunction) {
  const adminToken = process.env.ADMIN_ACCESS_TOKEN;

  // If no admin token is configured, skip token check
  if (!adminToken) {
    return next();
  }

  // Check for token in various places
  const tokenFromHeader = req.headers['x-admin-token'] as string;
  const tokenFromQuery = req.query.token as string;
  const tokenFromBody = req.body?.accessToken as string;

  const providedToken = tokenFromHeader || tokenFromQuery || tokenFromBody;

  // If token matches, create admin session
  if (providedToken && providedToken === adminToken) {
    console.log('Valid admin access token provided, creating admin session');

    // Create admin session
    req.session.isAdmin = true;
    req.session.username = 'admin';
    req.session.rememberMe = false;
    req.session.requiresTwoFactor = false;
    req.session.twoFactorVerified = false;
    req.session.userId = 1; // Default admin user ID

    // Set cookie expiration
    req.session.cookie.maxAge = 24 * 60 * 60 * 1000; // 24 hours

    return next();
  }

  // No valid token provided, continue with normal authentication
  next();
}
