import React from 'react';
import { Route, Redirect } from 'wouter';
import { useAdminAuth } from '@/hooks/use-admin-auth';
import { Loader2 } from 'lucide-react';

interface AdminRouteProps {
  path: string;
  component: React.ComponentType;
}

export function AdminRoute({ path, component: Component }: AdminRouteProps) {
  const { isAuthenticated, isLoading } = useAdminAuth();
  
  return (
    <Route path={path}>
      {() => {
        if (isLoading) {
          return (
            <div className="flex items-center justify-center h-screen">
              <Loader2 className="h-10 w-10 animate-spin text-primary" />
            </div>
          );
        }
        
        if (!isAuthenticated) {
          return <Redirect to="/admin/login" />;
        }
        
        return <Component />;
      }}
    </Route>
  );
}