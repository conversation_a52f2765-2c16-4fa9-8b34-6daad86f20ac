import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Migration to add payment fields to products table
async function addPaymentFieldsToProducts() {
  const dbPath = path.join(__dirname, '../database.sqlite');
  const sqlite = new Database(dbPath);
  const db = drizzle(sqlite);

  console.log('Adding payment fields to products table...');

  try {
    // Add payment method fields to products table
    sqlite.exec(`
      ALTER TABLE products ADD COLUMN payment_method TEXT DEFAULT 'custom-link';
      ALTER TABLE products ADD COLUMN custom_payment_link_id TEXT;
      ALTER TABLE products ADD COLUMN trial_custom_payment_link_id TEXT;
      ALTER TABLE products ADD COLUMN embed_code_id TEXT;
    `);

    // Remove appType and macAddress from invoices table
    console.log('Removing appType and macAddress from invoices table...');
    
    // Create new invoices table without appType and macAddress
    sqlite.exec(`
      CREATE TABLE invoices_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        full_name TEXT NOT NULL,
        email TEXT NOT NULL,
        product_id INTEGER NOT NULL,
        amount TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        custom_checkout_page_id INTEGER,
        country TEXT
      );
    `);

    // Copy data from old table to new table
    sqlite.exec(`
      INSERT INTO invoices_new (
        id, full_name, email, product_id, amount, status, 
        created_at, updated_at, custom_checkout_page_id, country
      )
      SELECT 
        id, full_name, email, product_id, amount, status, 
        created_at, updated_at, custom_checkout_page_id, country
      FROM invoices;
    `);

    // Drop old table and rename new table
    sqlite.exec(`
      DROP TABLE invoices;
      ALTER TABLE invoices_new RENAME TO invoices;
    `);

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    sqlite.close();
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  addPaymentFieldsToProducts()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export { addPaymentFieldsToProducts };
