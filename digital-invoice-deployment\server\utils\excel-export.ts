import ExcelJS from 'exceljs';
import { AllowedEmail } from '../../shared/schema';

/**
 * Utility function to export allowed emails to Excel
 * @param emails List of allowed emails to export
 * @returns Buffer containing the Excel file
 */
export async function exportAllowedEmailsToExcel(emails: AllowedEmail[]): Promise<Buffer> {
  // Create a new workbook
  const workbook = new ExcelJS.Workbook();

  // Add a worksheet
  const worksheet = workbook.addWorksheet('Allowed Emails');

  // Define columns
  worksheet.columns = [
    { header: 'Email', key: 'email', width: 30 },
    { header: 'Last Subject', key: 'lastSubject', width: 40 },
    { header: 'SMTP Provider', key: 'smtpProvider', width: 20 },
    { header: 'Last Updated', key: 'lastUpdated', width: 20 },
    { header: 'Created At', key: 'createdAt', width: 20 },
    { header: 'Notes', key: 'notes', width: 40 }
  ];

  // Add header row with styling
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE0E0E0' }
  };

  // Add data rows
  emails.forEach((email) => {
    // Clean notes to remove sensitive information
    const cleanedNotes = email.notes ? sanitizeNotes(email.notes) : '';

    worksheet.addRow({
      email: email.email,
      lastSubject: email.lastSubject || '',
      smtpProvider: email.smtpProvider || '',
      lastUpdated: formatDate(email.lastUpdated),
      createdAt: formatDate(email.createdAt),
      notes: cleanedNotes
    });
  });

  // Apply borders to all cells
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });

  // Auto-filter for all columns
  worksheet.autoFilter = {
    from: { row: 1, column: 1 },
    to: { row: 1, column: 6 }
  };

  // Write to buffer
  return await workbook.xlsx.writeBuffer();
}

/**
 * Format date string to a more readable format
 * @param dateString ISO date string
 * @returns Formatted date string
 */
function formatDate(dateString?: string): string {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return dateString;
  }
}

/**
 * Sanitize notes to remove sensitive information
 * @param notes Original notes text
 * @returns Sanitized notes text
 */
function sanitizeNotes(notes: string): string {
  if (!notes) return '';

  // Remove M3U links
  let sanitized = notes.replace(/https?:\/\/[^\s]*\.m3u[^\s]*/gi, '[M3U LINK REMOVED]');

  // Remove anything that looks like a password
  sanitized = sanitized.replace(/password\s*[:=]\s*[^\s,;]*/gi, 'password: [REMOVED]');
  sanitized = sanitized.replace(/pass\s*[:=]\s*[^\s,;]*/gi, 'pass: [REMOVED]');
  sanitized = sanitized.replace(/pwd\s*[:=]\s*[^\s,;]*/gi, 'pwd: [REMOVED]');

  // Remove potential credentials in URL format
  sanitized = sanitized.replace(/https?:\/\/[^:]+:[^@]+@/gi, '[CREDENTIALS REMOVED]');

  return sanitized;
}
