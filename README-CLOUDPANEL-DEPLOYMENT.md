# CloudPanel.io Deployment Guide

This guide provides step-by-step instructions for deploying your Digital Invoice application to CloudPanel.io with both SQLite and MySQL database options.

## Prerequisites

- CloudPanel.io server with Node.js support
- SSH access to your server
- Domain name pointed to your server
- Basic knowledge of Linux commands

## Project Overview

Your application is a Node.js/Express application with:
- **Frontend**: React with Vite build system
- **Backend**: Express.js server
- **Database**: Currently SQLite (with MySQL support built-in)
- **Port**: 3001 (configurable)

## Option 1: Deploy with SQLite (Recommended for Small to Medium Traffic)

### Step 1: Prepare Your Local Project

1. **Build the production version locally:**
```bash
npm run build:prod
```

2. **Create production environment file:**
Create `.env.production` with:
```env
DATABASE_URL=sqlite:./data.db
SESSION_SECRET=your-secure-random-session-secret-here
NODE_ENV=production
SECURE_COOKIES=true
ADMIN_ACCESS_TOKEN=your-secure-admin-token-here
```

### Step 2: Upload to CloudPanel Server

1. **Create a new site in CloudPanel:**
   - Go to CloudPanel dashboard
   - Click "Sites" → "Add Site"
   - Choose "Node.js" as the application type
   - Set your domain name
   - Set Node.js version to 18 or higher

2. **Upload your project files:**
```bash
# Compress your project (exclude node_modules)
tar -czf digital-invoice.tar.gz --exclude=node_modules --exclude=.git .

# Upload to server via SCP
scp digital-invoice.tar.gz root@your-server-ip:/home/<USER>/htdocs/

# SSH into server
ssh root@your-server-ip

# Navigate to site directory
cd /home/<USER>/htdocs/

# Extract files
tar -xzf digital-invoice.tar.gz
rm digital-invoice.tar.gz
```

### Step 3: Install Dependencies and Configure

1. **Install Node.js dependencies:**
```bash
npm install --production
```

2. **Copy environment file:**
```bash
cp .env.production .env
```

3. **Set proper permissions:**
```bash
chown -R your-site-user:your-site-user /home/<USER>/htdocs/
chmod 755 /home/<USER>/htdocs/
chmod 644 /home/<USER>/htdocs/data.db  # If database file exists
```

### Step 4: Configure CloudPanel Application

1. **In CloudPanel dashboard:**
   - Go to your site settings
   - Set "App Port" to `3001`
   - Set "Startup File" to `dist/index.js`
   - Enable "Auto Start"

2. **Set environment variables in CloudPanel:**
   - Add all variables from your `.env` file
   - Ensure `NODE_ENV=production`

### Step 5: Database Setup

1. **Initialize database:**
```bash
# The app will automatically create and initialize the SQLite database
# when it starts for the first time
npm run start
```

2. **Backup your local database (if you have existing data):**
```bash
# On your local machine, copy the database
scp ./data.db root@your-server-ip:/home/<USER>/htdocs/
```

### Step 6: Start the Application

1. **Start via CloudPanel:**
   - Go to your site in CloudPanel
   - Click "Start" to start the Node.js application

2. **Verify deployment:**
   - Visit your domain
   - Check CloudPanel logs for any errors

## Option 2: Deploy with MySQL (Recommended for High Traffic)

### Step 1: Create MySQL Database

1. **In CloudPanel dashboard:**
   - Go to "Databases" → "Add Database"
   - Create a new MySQL database
   - Note the database name, username, and password

### Step 2: Prepare Environment Configuration

1. **Create `.env.production` with MySQL:**
```env
DATABASE_URL=mysql://username:password@localhost:3306/database_name
SESSION_SECRET=your-secure-random-session-secret-here
NODE_ENV=production
SECURE_COOKIES=true
ADMIN_ACCESS_TOKEN=your-secure-admin-token-here
```

### Step 3: Migrate Data from SQLite to MySQL

1. **Install MySQL client tools:**
```bash
npm install -g mysql2
```

2. **Export SQLite data (on local machine):**
```bash
# Create a backup script
node -e "
const Database = require('better-sqlite3');
const fs = require('fs');
const db = new Database('./data.db');

// Get all table names
const tables = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table'\").all();

let sqlDump = '';
tables.forEach(table => {
  const rows = db.prepare(\`SELECT * FROM \${table.name}\`).all();
  if (rows.length > 0) {
    rows.forEach(row => {
      const columns = Object.keys(row).join(', ');
      const values = Object.values(row).map(v => 
        v === null ? 'NULL' : 
        typeof v === 'string' ? \`'\${v.replace(/'/g, \"''\")}\` : v
      ).join(', ');
      sqlDump += \`INSERT INTO \${table.name} (\${columns}) VALUES (\${values});\n\`;
    });
  }
});

fs.writeFileSync('./data-export.sql', sqlDump);
console.log('Data exported to data-export.sql');
"
```

3. **Upload and import to MySQL:**
```bash
# Upload the SQL dump
scp data-export.sql root@your-server-ip:/tmp/

# SSH to server and import
ssh root@your-server-ip
mysql -u username -p database_name < /tmp/data-export.sql
```

### Step 4: Deploy Application

Follow the same steps as SQLite deployment (Steps 2-6) but use the MySQL environment configuration.

## Post-Deployment Configuration

### SSL Certificate Setup

1. **In CloudPanel:**
   - Go to your site settings
   - Click "SSL/TLS"
   - Enable "Let's Encrypt" for free SSL certificate

### Nginx Configuration (if needed)

CloudPanel automatically configures Nginx, but you may need custom rules:

```nginx
# Custom Nginx rules for your site
location /api/ {
    proxy_pass http://127.0.0.1:3001;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
}
```

### Monitoring and Logs

1. **View application logs:**
```bash
# In CloudPanel dashboard
# Go to your site → Logs → Application Logs
```

2. **Monitor performance:**
   - Use CloudPanel's built-in monitoring
   - Check CPU and memory usage
   - Monitor database performance

## Troubleshooting

### Common Issues

1. **Application won't start:**
   - Check Node.js version compatibility
   - Verify all environment variables are set
   - Check application logs in CloudPanel

2. **Database connection errors:**
   - Verify DATABASE_URL format
   - Check database credentials
   - Ensure database exists and is accessible

3. **File permission issues:**
```bash
# Fix permissions
chown -R your-site-user:your-site-user /home/<USER>/htdocs/
chmod -R 755 /home/<USER>/htdocs/
```

4. **Build issues:**
```bash
# Rebuild on server if needed
npm run build:prod
```

### Performance Optimization

1. **Enable PM2 for process management:**
```bash
npm install -g pm2
pm2 start dist/index.js --name "digital-invoice"
pm2 startup
pm2 save
```

2. **Database optimization:**
   - For SQLite: Regular VACUUM operations
   - For MySQL: Optimize tables and indexes

## Backup Strategy

### SQLite Backup
```bash
# Daily backup script
cp /home/<USER>/htdocs/data.db /home/<USER>/backups/data-$(date +%Y%m%d).db
```

### MySQL Backup
```bash
# Daily backup script
mysqldump -u username -p database_name > /home/<USER>/backups/backup-$(date +%Y%m%d).sql
```

## Security Considerations

1. **Environment Variables:**
   - Use strong, unique SESSION_SECRET
   - Change default ADMIN_ACCESS_TOKEN
   - Enable SECURE_COOKIES in production

2. **File Permissions:**
   - Restrict access to sensitive files
   - Regular security updates

3. **Database Security:**
   - Use strong database passwords
   - Limit database user permissions
   - Regular security patches

## Support

For issues specific to:
- **CloudPanel**: Check CloudPanel documentation
- **Application**: Review application logs and error messages
- **Database**: Verify connection strings and permissions

Your application should now be successfully deployed and running on CloudPanel.io!

## Advanced Configuration

### Environment Variables Reference

Here's a complete list of environment variables your application uses:

```env
# Database Configuration
DATABASE_URL=sqlite:./data.db  # or mysql://user:pass@host:port/db

# Security
SESSION_SECRET=your-secure-session-secret-minimum-32-characters
ADMIN_ACCESS_TOKEN=your-secure-admin-token
SECURE_COOKIES=true  # Set to true in production with HTTPS

# Application
NODE_ENV=production
USE_DATABASE=true

# Optional: Email Configuration (if using SMTP)
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Your App Name

# Optional: PayPal Configuration (if using PayPal payments)
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_ENVIRONMENT=production  # or sandbox for testing
```

### Database Migration Scripts

#### SQLite to MySQL Migration Script

Create `migrate-sqlite-to-mysql.js`:

```javascript
const Database = require('better-sqlite3');
const mysql = require('mysql2/promise');
const fs = require('fs');

async function migrateSQLiteToMySQL() {
  // SQLite connection
  const sqliteDb = new Database('./data.db');

  // MySQL connection
  const mysqlDb = await mysql.createConnection({
    host: 'localhost',
    user: 'your-username',
    password: 'your-password',
    database: 'your-database'
  });

  try {
    // Get all tables from SQLite
    const tables = sqliteDb.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();

    for (const table of tables) {
      console.log(`Migrating table: ${table.name}`);

      // Get all data from SQLite table
      const rows = sqliteDb.prepare(`SELECT * FROM ${table.name}`).all();

      if (rows.length > 0) {
        // Get column names
        const columns = Object.keys(rows[0]);
        const placeholders = columns.map(() => '?').join(', ');

        // Insert data into MySQL
        for (const row of rows) {
          const values = columns.map(col => row[col]);
          await mysqlDb.execute(
            `INSERT INTO ${table.name} (${columns.join(', ')}) VALUES (${placeholders})`,
            values
          );
        }
      }
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    sqliteDb.close();
    await mysqlDb.end();
  }
}

migrateSQLiteToMySQL();
```

### Custom Startup Scripts

#### PM2 Ecosystem File

Create `ecosystem.config.js` for PM2 process management:

```javascript
module.exports = {
  apps: [{
    name: 'digital-invoice',
    script: './dist/index.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

Start with PM2:
```bash
pm2 start ecosystem.config.js
pm2 startup
pm2 save
```

### Automated Deployment Script

Create `deploy.sh` for automated deployment:

```bash
#!/bin/bash

# Automated deployment script for CloudPanel.io
set -e

echo "🚀 Starting deployment..."

# Variables
SITE_USER="your-site-user"
SITE_PATH="/home/<USER>/htdocs"
BACKUP_PATH="/home/<USER>/backups"

# Create backup
echo "📦 Creating backup..."
mkdir -p $BACKUP_PATH
if [ -f "$SITE_PATH/data.db" ]; then
    cp "$SITE_PATH/data.db" "$BACKUP_PATH/data-$(date +%Y%m%d-%H%M%S).db"
fi

# Stop application
echo "⏹️ Stopping application..."
pm2 stop digital-invoice || true

# Install dependencies
echo "📥 Installing dependencies..."
cd $SITE_PATH
npm install --production

# Build application
echo "🔨 Building application..."
npm run build:prod

# Database migrations (if needed)
echo "🗄️ Running database migrations..."
npm run db:push

# Start application
echo "▶️ Starting application..."
pm2 start ecosystem.config.js

echo "✅ Deployment completed successfully!"
echo "🌐 Your application is now running at your domain"
```

Make it executable:
```bash
chmod +x deploy.sh
```

### Health Check and Monitoring

#### Health Check Endpoint

Your application includes built-in health monitoring. You can check:

```bash
# Check if application is running
curl http://your-domain.com/api/health

# Check database connectivity
curl http://your-domain.com/api/health/db
```

#### Monitoring Script

Create `monitor.sh`:

```bash
#!/bin/bash

# Simple monitoring script
DOMAIN="your-domain.com"
LOG_FILE="/var/log/app-monitor.log"

check_health() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local response=$(curl -s -o /dev/null -w "%{http_code}" http://$DOMAIN/api/health)

    if [ "$response" = "200" ]; then
        echo "[$timestamp] ✅ Application is healthy" >> $LOG_FILE
    else
        echo "[$timestamp] ❌ Application health check failed (HTTP $response)" >> $LOG_FILE
        # Restart application
        pm2 restart digital-invoice
        echo "[$timestamp] 🔄 Application restarted" >> $LOG_FILE
    fi
}

# Run health check
check_health
```

Add to crontab for regular monitoring:
```bash
# Check every 5 minutes
*/5 * * * * /path/to/monitor.sh
```

### Backup Automation

#### Automated Backup Script

Create `backup.sh`:

```bash
#!/bin/bash

# Automated backup script
SITE_USER="your-site-user"
SITE_PATH="/home/<USER>/htdocs"
BACKUP_PATH="/home/<USER>/backups"
RETENTION_DAYS=30

# Create backup directory
mkdir -p $BACKUP_PATH

# Backup SQLite database
if [ -f "$SITE_PATH/data.db" ]; then
    echo "📦 Backing up SQLite database..."
    cp "$SITE_PATH/data.db" "$BACKUP_PATH/sqlite-$(date +%Y%m%d-%H%M%S).db"
fi

# Backup MySQL database (if using MySQL)
if [ ! -z "$MYSQL_DATABASE" ]; then
    echo "📦 Backing up MySQL database..."
    mysqldump -u $MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE > "$BACKUP_PATH/mysql-$(date +%Y%m%d-%H%M%S).sql"
fi

# Backup uploaded files
echo "📦 Backing up uploaded files..."
tar -czf "$BACKUP_PATH/uploads-$(date +%Y%m%d-%H%M%S).tar.gz" -C "$SITE_PATH" uploads/

# Clean old backups
echo "🧹 Cleaning old backups..."
find $BACKUP_PATH -name "*.db" -mtime +$RETENTION_DAYS -delete
find $BACKUP_PATH -name "*.sql" -mtime +$RETENTION_DAYS -delete
find $BACKUP_PATH -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

echo "✅ Backup completed successfully!"
```

Schedule daily backups:
```bash
# Add to crontab - daily at 2 AM
0 2 * * * /path/to/backup.sh
```

### Performance Tuning

#### Node.js Optimization

Add to your `package.json` scripts:

```json
{
  "scripts": {
    "start:optimized": "node --max-old-space-size=1024 --optimize-for-size dist/index.js"
  }
}
```

#### Database Optimization

For SQLite:
```bash
# Optimize SQLite database
sqlite3 data.db "VACUUM; ANALYZE;"
```

For MySQL:
```sql
-- Optimize all tables
OPTIMIZE TABLE users, products, invoices, custom_checkout_pages, allowed_emails, email_templates;

-- Analyze tables for better query planning
ANALYZE TABLE users, products, invoices, custom_checkout_pages, allowed_emails, email_templates;
```

### SSL and Security Hardening

#### Additional Nginx Security Headers

Add to your Nginx configuration:

```nginx
# Security headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

# Hide Nginx version
server_tokens off;
```

#### Firewall Configuration

```bash
# Allow only necessary ports
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

This comprehensive guide should cover all aspects of deploying your Digital Invoice application to CloudPanel.io successfully!
